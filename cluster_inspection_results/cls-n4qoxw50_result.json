{"ClusterName": "xiaoxun-tke", "ClusterID": "cls-n4qoxw50", "ClusterRegion": "ap-guangzhou", "ClusterType": "tke", "AppID": 1300797578, "Description": "", "MetaClusterID": "cls-1s75hexc", "State": "", "TotalCLBCount": 2, "TotalServiceCount": 44, "TotalIngressCount": 2, "TotalTKEServiceConfigCount": 0, "ServiceCIDR": "", "NetworkType": "", "KubeProxyMode": "", "K8SVersion": "", "VpcID": "", "SubnetID": "", "ServiceControllerImage": "ccr.ccs.tencentyun.com/tkeimages/service-controller:v2.3.1", "ServiceControllerVersion": "v2.3.1", "ServiceAvailableReplicas": 2, "ServiceExpectReplicas": 2, "ServiceArgs": "/service-controller;--backend-quota=0;--clusterName=cls-n4qoxw50;--kubeconfig=/root/.kube/config;--listener-quota=0;--reuse-flag=false", "ServiceImagePullPolicy": "Always", "ServiceResource": "&ResourceRequirements{Limits:ResourceList{cpu: {{400 -3} {<nil>} 400m DecimalSI},memory: {{805306368 0} {<nil>} 805306368 DecimalSI},},Requests:ResourceList{cpu: {{36 -3} {<nil>} 36m DecimalSI},memory: {{93952409 0} {<nil>} 93952409 DecimalSI},},}", "ServiceConfigMap": "LOADBALANCER_CRD_SUPPORT:true;REUSE_LOADBALANCER:false;VERSION:v2.3.1", "IngressControllerImage": "ccr.ccs.tencentyun.com/tkeimages/ingress-controller:v2.2.3", "IngressControllerVersion": "v2.2.3", "IngressAvailableReplicas": 1, "IngressExpectReplicas": 1, "IngressArgs": "--cluster-name=cls-n4qoxw50", "IngressImagePullPolicy": "Always", "IngressResource": "&ResourceRequirements{Limits:ResourceList{},Requests:ResourceList{},}", "IngressConfigMap": "LOADBALANCER_CRD_SUPPORT:true;SILENT_START:false;VERSION:v2.2.6"}