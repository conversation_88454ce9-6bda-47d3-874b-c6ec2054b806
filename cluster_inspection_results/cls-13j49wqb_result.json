{"ClusterName": "SH-Cluster01", "ClusterID": "cls-13j49wqb", "ClusterRegion": "ap-shanghai", "ClusterType": "tke", "AppID": 1331741259, "Description": "", "MetaClusterID": "cls-gofhtn3t", "State": "", "TotalCLBCount": 8, "TotalServiceCount": 20, "TotalIngressCount": 5, "TotalTKEServiceConfigCount": 3, "ServiceCIDR": "", "NetworkType": "", "KubeProxyMode": "", "K8SVersion": "", "VpcID": "", "SubnetID": "", "ServiceControllerImage": "ccr.ccs.tencentyun.com/tkeimages/service-controller:v2.4.2", "ServiceControllerVersion": "v2.4.2", "ServiceAvailableReplicas": 2, "ServiceExpectReplicas": 2, "ServiceArgs": "/service-controller;--backend-quota=0;--clusterName=cls-13j49wqb;--kubeconfig=/root/.kube/config;--listener-quota=0;--reuse-flag=false", "ServiceImagePullPolicy": "Always", "ServiceResource": "&ResourceRequirements{Limits:ResourceList{cpu: {{200 -3} {<nil>} 200m DecimalSI},memory: {{536870912 0} {<nil>} 536870912 DecimalSI},},Requests:ResourceList{cpu: {{30 -3} {<nil>} 30m DecimalSI},memory: {{67108864 0} {<nil>} 67108864 DecimalSI},},}", "ServiceConfigMap": "REUSE_LOADBALANCER:false;VERSION:v2.4.2;EnableNodeGracefulDeletion:false;GlobalRouteDirectAccess:true;LOADBALANCER_CRD_SUPPORT:true", "IngressControllerImage": "ccr.ccs.tencentyun.com/tkeimages/ingress-controller:v2.4.2", "IngressControllerVersion": "v2.4.2", "IngressAvailableReplicas": 2, "IngressExpectReplicas": 2, "IngressArgs": "--cluster-name=cls-13j49wqb", "IngressImagePullPolicy": "Always", "IngressResource": "&ResourceRequirements{Limits:ResourceList{cpu: {{4 0} {<nil>} 4 DecimalSI},memory: {{8589934592 0} {<nil>}  BinarySI},},Requests:ResourceList{cpu: {{50 -3} {<nil>} 50m DecimalSI},memory: {{104857600 0} {<nil>} 100Mi BinarySI},},}", "IngressConfigMap": "LOADBALANCER_CRD_SUPPORT:true;SILENT_START:false;VERSION:v2.4.2"}