{"ClusterName": "腾讯测试环境", "ClusterID": "cls-2<PERSON>vit7", "ClusterRegion": "ap-beijing", "ClusterType": "tke", "AppID": 1306197496, "Description": "", "MetaClusterID": "cls-0lk56led", "State": "", "TotalCLBCount": 51, "TotalServiceCount": 2048, "TotalIngressCount": 9, "TotalTKEServiceConfigCount": 11, "RiskService": ["clusterID/cls-2nyavit7/namespace/testing/namecrm-enterprise-wechat/lbid/lb-7okryj7t"], "RiskIngress": null, "ServiceCIDR": "", "NetworkType": "", "KubeProxyMode": "", "K8SVersion": "", "VpcID": "", "SubnetID": "", "ServiceControllerImage": "ccr.ccs.tencentyun.com/tkeimages/service-controller:v2.4.0", "ServiceControllerVersion": "v2.4.0", "ServiceAvailableReplicas": 2, "ServiceExpectReplicas": 2, "ServiceArgs": "/service-controller;--backend-quota=0;--clusterName=cls-2nyavit7;--kubeconfig=/root/.kube/config;--listener-quota=0;--reuse-flag=false", "ServiceImagePullPolicy": "Always", "ServiceResource": "&ResourceRequirements{Limits:ResourceList{cpu: {{1600 -3} {<nil>} 1600m DecimalSI},memory: {{1811939328 0} {<nil>} 1811939328 DecimalSI},},Requests:ResourceList{cpu: {{51 -3} {<nil>} 51m DecimalSI},memory: {{131533372 0} {<nil>} 131533372 DecimalSI},},}", "ServiceConfigMap": "REUSE_LOADBALANCER:false;VERSION:v2.4.0;GlobalRouteDirectAccess:true;LOADBALANCER_CRD_SUPPORT:true", "IngressControllerImage": "ccr.ccs.tencentyun.com/tkeimages/ingress-controller:v2.4.0", "IngressControllerVersion": "v2.4.0", "IngressAvailableReplicas": 2, "IngressExpectReplicas": 2, "IngressArgs": "--cluster-name=cls-2nyavit7", "IngressImagePullPolicy": "Always", "IngressResource": "&ResourceRequirements{Limits:ResourceList{cpu: {{4 0} {<nil>} 4 DecimalSI},memory: {{8589934592 0} {<nil>}  BinarySI},},Requests:ResourceList{cpu: {{50 -3} {<nil>} 50m DecimalSI},memory: {{104857600 0} {<nil>} 100Mi BinarySI},},}", "IngressConfigMap": "LOADBALANCER_CRD_SUPPORT:true;SILENT_START:false;VERSION:v2.4.0"}