{"ClusterName": "HA-JR-PROD", "ClusterID": "cls-qvk25nxu", "ClusterRegion": "ap-guangzhou", "ClusterType": "tke", "AppID": 1302914566, "Description": "", "MetaClusterID": "cls-1s75hexc", "State": "", "TotalCLBCount": 20, "TotalServiceCount": 463, "TotalIngressCount": 0, "TotalTKEServiceConfigCount": 1, "RiskService": null, "RiskIngress": null, "ServiceCIDR": "", "NetworkType": "", "KubeProxyMode": "", "K8SVersion": "", "VpcID": "", "SubnetID": "", "ServiceControllerImage": "ccr.ccs.tencentyun.com/tkeimages/service-controller:v2.1.3", "ServiceControllerVersion": "v2.1.3", "ServiceAvailableReplicas": 2, "ServiceExpectReplicas": 2, "ServiceArgs": "/service-controller;--backend-quota=0;--clusterName=cls-qvk25nxu;--listener-quota=0;--master=http://cls-qvk25nxu-apiserver-service:60001;--reuse-flag=false", "ServiceImagePullPolicy": "Always", "ServiceResource": "&ResourceRequirements{Limits:ResourceList{cpu: {{268 -3} {<nil>} 268m DecimalSI},memory: {{1073741824 0} {<nil>} 1Gi BinarySI},},Requests:ResourceList{cpu: {{67 -3} {<nil>} 67m DecimalSI},memory: {{268435456 0} {<nil>}  BinarySI},},}", "ServiceConfigMap": "VERSION:v2.3.3;LOADBALANCER_CRD_SUPPORT:true;REUSE_LOADBALANCER:false", "IngressControllerImage": "ccr.ccs.tencentyun.com/tkeimages/ingress-controller:v2.1.3", "IngressControllerVersion": "v2.1.3", "IngressAvailableReplicas": 1, "IngressExpectReplicas": 1, "IngressArgs": "--cluster-name=cls-qvk25nxu", "IngressImagePullPolicy": "Always", "IngressResource": "&ResourceRequirements{Limits:ResourceList{},Requests:ResourceList{},}", "IngressConfigMap": "LOADBALANCER_CRD_SUPPORT:true;SILENT_START:false;VERSION:v2.2.6"}