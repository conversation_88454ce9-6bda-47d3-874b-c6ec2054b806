{"ClusterName": "cnb-saas-prod-gz", "ClusterID": "cls-m29ot7ga", "ClusterRegion": "ap-guangzhou", "ClusterType": "tke", "AppID": 1318722668, "Description": "", "MetaClusterID": "cls-1s75hexc", "State": "", "TotalCLBCount": 51, "TotalServiceCount": 123, "TotalIngressCount": 7, "TotalTKEServiceConfigCount": 28, "RiskService": null, "RiskIngress": ["clusterID/cls-m29ot7ga/namespace/cnb-prod-a/namecnb-saas-nginx-data-ingress-ipv4/lbid/lb-kj11qcso", "clusterID/cls-m29ot7ga/namespace/cnb-prod-a/namecnb-saas-nginx-data-ingress-ipv6/lbid/lb-iixl7usu", "clusterID/cls-m29ot7ga/namespace/cnb-prod-a/namecnb-saas-nginx-ingress-ipv4/lbid/lb-7xls32su", "clusterID/cls-m29ot7ga/namespace/cnb-prod-a/namecnb-saas-nginx-ingress-ipv6/lbid/lb-20iyid2a", "clusterID/cls-m29ot7ga/namespace/cnb-prod-a/namenginx-data-ingress/lbid/lb-2fk20bya", "clusterID/cls-m29ot7ga/namespace/cnb-prod-a/namenginx-ingress-gz6-gz7/lbid/lb-jbu7ufpi", "clusterID/cls-m29ot7ga/namespace/cnb-prod-a/namenginx-ingress-hk/lbid/lb-4z23xjcc"], "ServiceCIDR": "", "NetworkType": "", "KubeProxyMode": "", "K8SVersion": "", "VpcID": "", "SubnetID": "", "ServiceControllerImage": "ccr.ccs.tencentyun.com/tkeimages/service-controller:v2.4.2", "ServiceControllerVersion": "v2.4.2", "ServiceAvailableReplicas": 2, "ServiceExpectReplicas": 2, "ServiceArgs": "/service-controller;--backend-quota=0;--clusterName=cls-m29ot7ga;--kubeconfig=/root/.kube/config;--listener-quota=0;--reuse-flag=false", "ServiceImagePullPolicy": "Always", "ServiceResource": "&ResourceRequirements{Limits:ResourceList{cpu: {{200 -3} {<nil>} 200m DecimalSI},memory: {{536870912 0} {<nil>} 536870912 DecimalSI},},Requests:ResourceList{cpu: {{30 -3} {<nil>} 30m DecimalSI},memory: {{67108864 0} {<nil>} 67108864 DecimalSI},},}", "ServiceConfigMap": "EnableNodeGracefulDeletion:false;LOADBALANCER_CRD_SUPPORT:true;REUSE_LOADBALANCER:false;VERSION:v2.4.2", "IngressControllerImage": "ccr.ccs.tencentyun.com/tkeimages/ingress-controller:v2.4.2", "IngressControllerVersion": "v2.4.2", "IngressAvailableReplicas": 2, "IngressExpectReplicas": 2, "IngressArgs": "--cluster-name=cls-m29ot7ga", "IngressImagePullPolicy": "Always", "IngressResource": "&ResourceRequirements{Limits:ResourceList{cpu: {{4 0} {<nil>} 4 DecimalSI},memory: {{8589934592 0} {<nil>}  BinarySI},},Requests:ResourceList{cpu: {{50 -3} {<nil>} 50m DecimalSI},memory: {{104857600 0} {<nil>} 100Mi BinarySI},},}", "IngressConfigMap": "LOADBALANCER_CRD_SUPPORT:true;SILENT_START:false;VERSION:v2.4.2"}