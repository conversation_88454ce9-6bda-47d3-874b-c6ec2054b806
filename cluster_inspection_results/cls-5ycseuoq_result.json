{"ClusterName": "aigc", "ClusterID": "cls-5ycseuoq", "ClusterRegion": "ap-singapore", "ClusterType": "tke", "AppID": 1300342680, "Description": "", "MetaClusterID": "cls-75j3tu60", "State": "", "TotalCLBCount": 247, "TotalServiceCount": 251, "TotalIngressCount": 20, "TotalTKEServiceConfigCount": 32, "ServiceCIDR": "", "NetworkType": "", "KubeProxyMode": "", "K8SVersion": "", "VpcID": "", "SubnetID": "", "ServiceControllerImage": "sgccr.ccs.tencentyun.com/tkeimages/service-controller:v2.3.1", "ServiceControllerVersion": "v2.3.1", "ServiceAvailableReplicas": 2, "ServiceExpectReplicas": 2, "ServiceArgs": "/service-controller;--backend-quota=0;--clusterName=cls-5ycseuoq;--kubeconfig=/root/.kube/config;--listener-quota=0;--reuse-flag=false", "ServiceImagePullPolicy": "Always", "ServiceResource": "&ResourceRequirements{Limits:ResourceList{cpu: {{200 -3} {<nil>} 200m DecimalSI},memory: {{536870912 0} {<nil>} 536870912 DecimalSI},},Requests:ResourceList{cpu: {{30 -3} {<nil>} 30m DecimalSI},memory: {{67108864 0} {<nil>} 67108864 DecimalSI},},}", "ServiceConfigMap": "LOADBALANCER_CRD_SUPPORT:true;REUSE_LOADBALANCER:false;VERSION:v2.3.1", "IngressControllerImage": "ccr.ccs.tencentyun.com/tkeimages/ingress-controller:v2.2.3", "IngressControllerVersion": "v2.2.3", "IngressAvailableReplicas": 1, "IngressExpectReplicas": 1, "IngressArgs": "--cluster-name=cls-5ycseuoq", "IngressImagePullPolicy": "Always", "IngressResource": "&ResourceRequirements{Limits:ResourceList{},Requests:ResourceList{},}", "IngressConfigMap": "VERSION:v2.2.5;LOADBALANCER_CRD_SUPPORT:true;SILENT_START:false"}