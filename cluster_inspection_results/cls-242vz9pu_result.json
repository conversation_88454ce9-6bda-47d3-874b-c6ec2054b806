{"ClusterName": "腾讯设计-体验", "ClusterID": "cls-242vz9pu", "ClusterRegion": "ap-guangzhou", "ClusterType": "tke", "AppID": 1258344699, "Description": "", "MetaClusterID": "cls-pgl5mqfo", "State": "", "TotalCLBCount": 2, "TotalServiceCount": 52, "TotalIngressCount": 1, "TotalTKEServiceConfigCount": 1, "ServiceCIDR": "", "NetworkType": "", "KubeProxyMode": "", "K8SVersion": "", "VpcID": "", "SubnetID": "", "ServiceControllerImage": "ccr.ccs.tencentyun.com/tkeimages/service-controller:v2.5.0", "ServiceControllerVersion": "v2.5.0", "ServiceAvailableReplicas": 2, "ServiceExpectReplicas": 2, "ServiceArgs": "/service-controller;--backend-quota=0;--clusterName=cls-242vz9pu;--kubeconfig=/root/.kube/config;--listener-quota=0", "ServiceImagePullPolicy": "IfNotPresent", "ServiceResource": "&ResourceRequirements{Limits:ResourceList{cpu: {{200 -3} {<nil>} 200m DecimalSI},memory: {{536870912 0} {<nil>} 536870912 DecimalSI},},Requests:ResourceList{cpu: {{30 -3} {<nil>} 30m DecimalSI},memory: {{52428800 0} {<nil>} 52428800 DecimalSI},},}", "ServiceConfigMap": "GlobalRouteDirectAccess:true;LOADBALANCER_CRD_SUPPORT:true;REUSE_LOADBALANCER:true;VERSION:v2.5.0;EnableNodeGracefulDeletion:false", "IngressControllerImage": "ccr.ccs.tencentyun.com/tkeimages/ingress-controller:v2.5.0", "IngressControllerVersion": "v2.5.0", "IngressAvailableReplicas": 2, "IngressExpectReplicas": 2, "IngressArgs": "--cluster-name=cls-242vz9pu", "IngressImagePullPolicy": "Always", "IngressResource": "&ResourceRequirements{Limits:ResourceList{cpu: {{4 0} {<nil>} 4 DecimalSI},memory: {{8589934592 0} {<nil>}  BinarySI},},Requests:ResourceList{cpu: {{50 -3} {<nil>} 50m DecimalSI},memory: {{104857600 0} {<nil>} 100Mi BinarySI},},}", "IngressConfigMap": "SILENT_START:false;VERSION:v2.5.0;LOADBALANCER_CRD_SUPPORT:true"}