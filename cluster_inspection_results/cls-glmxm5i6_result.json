{"ClusterName": "inbot", "ClusterID": "cls-glmxm5i6", "ClusterRegion": "ap-nanjing", "ClusterType": "tke", "AppID": 1313437761, "Description": "", "MetaClusterID": "cls-cnnoyaqm", "State": "", "TotalCLBCount": 7, "TotalServiceCount": 122, "TotalIngressCount": 6, "TotalTKEServiceConfigCount": 1, "ServiceCIDR": "", "NetworkType": "", "KubeProxyMode": "", "K8SVersion": "", "VpcID": "", "SubnetID": "", "ServiceControllerImage": "ccr.ccs.tencentyun.com/tkeimages/service-controller:v2.3.1", "ServiceControllerVersion": "v2.3.1", "ServiceAvailableReplicas": 2, "ServiceExpectReplicas": 2, "ServiceArgs": "/service-controller;--backend-quota=0;--clusterName=cls-glmxm5i6;--kubeconfig=/root/.kube/config;--listener-quota=0;--reuse-flag=false", "ServiceImagePullPolicy": "Always", "ServiceResource": "&ResourceRequirements{Limits:ResourceList{cpu: {{200 -3} {<nil>} 200m DecimalSI},memory: {{536870912 0} {<nil>} 536870912 DecimalSI},},Requests:ResourceList{cpu: {{30 -3} {<nil>} 30m DecimalSI},memory: {{67108864 0} {<nil>} 67108864 DecimalSI},},}", "ServiceConfigMap": "LOADBALANCER_CRD_SUPPORT:true;REUSE_LOADBALANCER:false;VERSION:v2.3.1", "IngressControllerImage": "ccr.ccs.tencentyun.com/tkeimages/ingress-controller:v2.1.3", "IngressControllerVersion": "v2.1.3", "IngressAvailableReplicas": 1, "IngressExpectReplicas": 1, "IngressArgs": "--cluster-name=cls-glmxm5i6", "IngressImagePullPolicy": "Always", "IngressResource": "&ResourceRequirements{Limits:ResourceList{},Requests:ResourceList{},}", "IngressConfigMap": "LOADBALANCER_CRD_SUPPORT:true;SILENT_START:false"}