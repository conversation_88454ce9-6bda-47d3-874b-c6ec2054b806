{"ClusterName": "aivlog_prod", "ClusterID": "cls-nsftw5og", "ClusterRegion": "na-siliconvalley", "ClusterType": "tke", "AppID": 1313805896, "Description": "", "MetaClusterID": "cls-1vxwfay6", "State": "", "TotalCLBCount": 3, "TotalServiceCount": 15, "TotalIngressCount": 2, "TotalTKEServiceConfigCount": 0, "RiskService": null, "RiskIngress": null, "ServiceCIDR": "", "NetworkType": "", "KubeProxyMode": "", "K8SVersion": "", "VpcID": "", "SubnetID": "", "ServiceControllerImage": "uswccr.ccs.tencentyun.com/tkeimages/service-controller:v2.4.2", "ServiceControllerVersion": "v2.4.2", "ServiceAvailableReplicas": 2, "ServiceExpectReplicas": 2, "ServiceArgs": "/service-controller;--backend-quota=0;--clusterName=cls-nsftw5og;--kubeconfig=/root/.kube/config;--listener-quota=0;--reuse-flag=false", "ServiceImagePullPolicy": "Always", "ServiceResource": "&ResourceRequirements{Limits:ResourceList{cpu: {{200 -3} {<nil>} 200m DecimalSI},memory: {{536870912 0} {<nil>} 536870912 DecimalSI},},Requests:ResourceList{cpu: {{30 -3} {<nil>} 30m DecimalSI},memory: {{52428800 0} {<nil>} 52428800 DecimalSI},},}", "ServiceConfigMap": "LOADBALANCER_CRD_SUPPORT:true;REUSE_LOADBALANCER:false;VERSION:v2.4.2;EnableNodeGracefulDeletion:false;GlobalRouteDirectAccess:true", "IngressControllerImage": "uswccr.ccs.tencentyun.com/tkeimages/ingress-controller:v2.4.2", "IngressControllerVersion": "v2.4.2", "IngressAvailableReplicas": 2, "IngressExpectReplicas": 2, "IngressArgs": "--cluster-name=cls-nsftw5og", "IngressImagePullPolicy": "Always", "IngressResource": "&ResourceRequirements{Limits:ResourceList{cpu: {{1 0} {<nil>} 1 DecimalSI},memory: {{1073741824 0} {<nil>} 1Gi BinarySI},},Requests:ResourceList{cpu: {{50 -3} {<nil>} 50m DecimalSI},memory: {{104857600 0} {<nil>} 100Mi BinarySI},},}", "IngressConfigMap": "LOADBALANCER_CRD_SUPPORT:true;SILENT_START:false;VERSION:v2.4.2"}