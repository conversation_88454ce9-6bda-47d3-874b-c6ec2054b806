{"ClusterName": "devops-tke-pot", "ClusterID": "cls-371qkhf6", "ClusterRegion": "ap-shenzhen", "ClusterType": "tke", "AppID": 1258344706, "Description": "", "MetaClusterID": "cls-mh1d842k", "State": "", "TotalCLBCount": 73, "TotalServiceCount": 195, "TotalIngressCount": 0, "TotalTKEServiceConfigCount": 11, "ServiceCIDR": "", "NetworkType": "", "KubeProxyMode": "", "K8SVersion": "", "VpcID": "", "SubnetID": "", "ServiceControllerImage": "ccr.ccs.tencentyun.com/tkeimages/service-controller:v2.3.1", "ServiceControllerVersion": "v2.3.1", "ServiceAvailableReplicas": 2, "ServiceExpectReplicas": 2, "ServiceArgs": "/service-controller;--backend-quota=0;--clusterName=cls-371qkhf6;--listener-quota=0;--master=http://cls-371qkhf6-apiserver-service:60001", "ServiceImagePullPolicy": "Always", "ServiceResource": "&ResourceRequirements{Limits:ResourceList{cpu: {{200 -3} {<nil>} 200m DecimalSI},memory: {{536870912 0} {<nil>} 536870912 DecimalSI},},Requests:ResourceList{cpu: {{30 -3} {<nil>} 30m DecimalSI},memory: {{67108864 0} {<nil>} 67108864 DecimalSI},},}", "ServiceConfigMap": "REUSE_LOADBALANCER:true;VERSION:v2.3.1;LOADBALANCER_CRD_SUPPORT:true", "IngressControllerImage": "ccr.ccs.tencentyun.com/tkeimages/ingress-controller:v2.1.3-2-g1936929", "IngressControllerVersion": "v2.1.3-2-g1936929", "IngressAvailableReplicas": 1, "IngressExpectReplicas": 1, "IngressArgs": "--cluster-name=cls-371qkhf6", "IngressImagePullPolicy": "Always", "IngressResource": "&ResourceRequirements{Limits:ResourceList{},Requests:ResourceList{},}", "IngressConfigMap": "SILENT_START:false;LOADBALANCER_CRD_SUPPORT:true"}