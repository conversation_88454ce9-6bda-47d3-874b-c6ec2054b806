{"ClusterName": "tke-hk-test-lit-lit-001", "ClusterID": "cls-2300v0c4", "ClusterRegion": "ap-hongkong", "ClusterType": "tke", "AppID": 1317461951, "Description": "", "MetaClusterID": "cls-ne0wmuuw", "State": "", "TotalCLBCount": 23, "TotalServiceCount": 109, "TotalIngressCount": 44, "TotalTKEServiceConfigCount": 20, "RiskService": null, "RiskIngress": null, "ServiceCIDR": "", "NetworkType": "", "KubeProxyMode": "", "K8SVersion": "", "VpcID": "", "SubnetID": "", "ServiceControllerImage": "hkccr.ccs.tencentyun.com/tkeimages/service-controller:v2.5.2", "ServiceControllerVersion": "v2.5.2", "ServiceAvailableReplicas": 2, "ServiceExpectReplicas": 2, "ServiceArgs": "/service-controller;--backend-quota=0;--clusterName=cls-2300v0c4;--kubeconfig=/root/.kube/config;--listener-quota=0", "ServiceImagePullPolicy": "Always", "ServiceResource": "&ResourceRequirements{Limits:ResourceList{cpu: {{200 -3} {<nil>} 200m DecimalSI},memory: {{536870912 0} {<nil>} 536870912 DecimalSI},},Requests:ResourceList{cpu: {{30 -3} {<nil>} 30m DecimalSI},memory: {{52428800 0} {<nil>} 52428800 DecimalSI},},}", "ServiceConfigMap": "LOADBALANCER_CRD_SUPPORT:true;REUSE_LOADBALANCER:true;VERSION:v2.5.2;EnableNodeGracefulDeletion:false", "IngressControllerImage": "hkccr.ccs.tencentyun.com/tkeimages/ingress-controller:v2.5.2", "IngressControllerVersion": "v2.5.2", "IngressAvailableReplicas": 2, "IngressExpectReplicas": 2, "IngressArgs": "--cluster-name=cls-2300v0c4", "IngressImagePullPolicy": "Always", "IngressResource": "&ResourceRequirements{Limits:ResourceList{cpu: {{4 0} {<nil>} 4 DecimalSI},memory: {{8589934592 0} {<nil>}  BinarySI},},Requests:ResourceList{cpu: {{50 -3} {<nil>} 50m DecimalSI},memory: {{104857600 0} {<nil>} 100Mi BinarySI},},}", "IngressConfigMap": "LOADBALANCER_CRD_SUPPORT:true;SILENT_START:false;VERSION:v2.5.2"}