{"ClusterName": "乐享测试-k8s集群", "ClusterID": "cls-dazrr6e2", "ClusterRegion": "ap-guangzhou", "ClusterType": "tke", "AppID": 1252005255, "Description": "", "MetaClusterID": "cls-1s75hexc", "State": "", "TotalCLBCount": 16, "TotalServiceCount": 220, "TotalIngressCount": 0, "TotalTKEServiceConfigCount": 0, "RiskService": null, "RiskIngress": ["clusterID/cls-dazrr6e2/namespace/default/namelxtest-gateway-public-keepalive/lbid/lb-rn7kb6o2"], "ServiceCIDR": "", "NetworkType": "", "KubeProxyMode": "", "K8SVersion": "", "VpcID": "", "SubnetID": "", "ServiceControllerImage": "ccr.ccs.tencentyun.com/tkeimages/service-controller:v2.1.3", "ServiceControllerVersion": "v2.1.3", "ServiceAvailableReplicas": 2, "ServiceExpectReplicas": 2, "ServiceArgs": "/service-controller;--backend-quota=0;--clusterName=cls-dazrr6e2;--listener-quota=0;--master=http://cls-dazrr6e2-apiserver-service:60001", "ServiceImagePullPolicy": "Always", "ServiceResource": "&ResourceRequirements{Limits:ResourceList{cpu: {{1 0} {<nil>} 1 DecimalSI},memory: {{1073741824 0} {<nil>} 1Gi BinarySI},},Requests:ResourceList{cpu: {{250 -3} {<nil>} 250m DecimalSI},memory: {{268435456 0} {<nil>}  BinarySI},},}", "ServiceConfigMap": "LOADBALANCER_CRD_SUPPORT:true;REUSE_LOADBALANCER:true;VERSION:v2.3.3", "IngressControllerImage": "ccr.ccs.tencentyun.com/tkeimages/ingress-controller:v2.2.3", "IngressControllerVersion": "v2.2.3", "IngressAvailableReplicas": 1, "IngressExpectReplicas": 1, "IngressArgs": "--cluster-name=cls-dazrr6e2", "IngressImagePullPolicy": "Always", "IngressResource": "&ResourceRequirements{Limits:ResourceList{},Requests:ResourceList{},}", "IngressConfigMap": "LOADBALANCER_CRD_SUPPORT:true;SILENT_START:false;VERSION:v2.2.6"}