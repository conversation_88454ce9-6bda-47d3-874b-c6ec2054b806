{"ClusterName": "bestway-cluster", "ClusterID": "cls-019ctobc", "ClusterRegion": "ap-nanjing", "ClusterType": "tke", "AppID": 1318166532, "Description": "", "MetaClusterID": "cls-cnnoyaqm", "State": "", "TotalCLBCount": 2, "TotalServiceCount": 16, "TotalIngressCount": 1, "TotalTKEServiceConfigCount": 0, "ServiceCIDR": "", "NetworkType": "", "KubeProxyMode": "", "K8SVersion": "", "VpcID": "", "SubnetID": "", "ServiceControllerImage": "ccr.ccs.tencentyun.com/tkeimages/service-controller:v2.5.0", "ServiceControllerVersion": "v2.5.0", "ServiceAvailableReplicas": 2, "ServiceExpectReplicas": 2, "ServiceArgs": "/service-controller;--backend-quota=0;--clusterName=cls-019ctobc;--kubeconfig=/root/.kube/config;--listener-quota=0", "ServiceImagePullPolicy": "IfNotPresent", "ServiceResource": "&ResourceRequirements{Limits:ResourceList{cpu: {{200 -3} {<nil>} 200m DecimalSI},memory: {{536870912 0} {<nil>} 536870912 DecimalSI},},Requests:ResourceList{cpu: {{30 -3} {<nil>} 30m DecimalSI},memory: {{52428800 0} {<nil>} 52428800 DecimalSI},},}", "ServiceConfigMap": "VERSION:v2.5.0;EnableNodeGracefulDeletion:false;GlobalRouteDirectAccess:true;LOADBALANCER_CRD_SUPPORT:true;REUSE_LOADBALANCER:true", "IngressControllerImage": "ccr.ccs.tencentyun.com/tkeimages/ingress-controller:v2.5.0", "IngressControllerVersion": "v2.5.0", "IngressAvailableReplicas": 1, "IngressExpectReplicas": 1, "IngressArgs": "--cluster-name=cls-019ctobc", "IngressImagePullPolicy": "Always", "IngressResource": "&ResourceRequirements{Limits:ResourceList{cpu: {{500 -3} {<nil>} 500m DecimalSI},memory: {{1073741824 0} {<nil>} 1Gi BinarySI},},Requests:ResourceList{cpu: {{50 -3} {<nil>} 50m DecimalSI},memory: {{104857600 0} {<nil>} 100Mi BinarySI},},}", "IngressConfigMap": "LOADBALANCER_CRD_SUPPORT:true;SILENT_START:false;VERSION:v2.5.0"}