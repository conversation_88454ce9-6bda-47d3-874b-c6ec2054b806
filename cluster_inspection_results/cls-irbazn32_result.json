{"ClusterName": "\ttke-hk-prod-lit-lit-001", "ClusterID": "cls-irbazn32", "ClusterRegion": "ap-hongkong", "ClusterType": "tke", "AppID": 1317461951, "Description": "", "MetaClusterID": "cls-ne0wmuuw", "State": "", "TotalCLBCount": 33, "TotalServiceCount": 126, "TotalIngressCount": 57, "TotalTKEServiceConfigCount": 21, "RiskService": null, "RiskIngress": null, "ServiceCIDR": "", "NetworkType": "", "KubeProxyMode": "", "K8SVersion": "", "VpcID": "", "SubnetID": "", "ServiceControllerImage": "hkccr.ccs.tencentyun.com/tkeimages/service-controller:v2.5.0", "ServiceControllerVersion": "v2.5.0", "ServiceAvailableReplicas": 2, "ServiceExpectReplicas": 2, "ServiceArgs": "/service-controller;--backend-quota=0;--clusterName=cls-irbazn32;--kubeconfig=/root/.kube/config;--listener-quota=0;--reuse-flag=false", "ServiceImagePullPolicy": "Always", "ServiceResource": "&ResourceRequirements{Limits:ResourceList{cpu: {{472 -3} {<nil>} 472m DecimalSI},memory: {{787497129 0} {<nil>} 787497129 DecimalSI},},Requests:ResourceList{cpu: {{236 -3} {<nil>} 236m DecimalSI},memory: {{524998086 0} {<nil>} 524998086 DecimalSI},},}", "ServiceConfigMap": "EnableNodeGracefulDeletion:false;LOADBALANCER_CRD_SUPPORT:true;REUSE_LOADBALANCER:false;VERSION:v2.5.0", "IngressControllerImage": "hkccr.ccs.tencentyun.com/tkeimages/ingress-controller:v2.2.8", "IngressControllerVersion": "v2.2.8", "IngressAvailableReplicas": 2, "IngressExpectReplicas": 2, "IngressArgs": "--cluster-name=cls-irbazn32", "IngressImagePullPolicy": "Always", "IngressResource": "&ResourceRequirements{Limits:ResourceList{cpu: {{4 0} {<nil>} 4 DecimalSI},memory: {{8589934592 0} {<nil>}  BinarySI},},Requests:ResourceList{cpu: {{50 -3} {<nil>} 50m DecimalSI},memory: {{104857600 0} {<nil>} 100Mi BinarySI},},}", "IngressConfigMap": "LOADBALANCER_CRD_SUPPORT:true;SILENT_START:false;VERSION:v2.5.0"}