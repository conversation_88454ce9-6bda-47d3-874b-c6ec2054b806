{"ClusterName": "prod-tpl", "ClusterID": "cls-gkohai36", "ClusterRegion": "ap-hongkong", "ClusterType": "tke", "AppID": 1307006278, "Description": "", "MetaClusterID": "cls-ne0wmuuw", "State": "", "TotalCLBCount": 3, "TotalServiceCount": 21, "TotalIngressCount": 2, "TotalTKEServiceConfigCount": 0, "RiskService": null, "RiskIngress": null, "ServiceCIDR": "", "NetworkType": "", "KubeProxyMode": "", "K8SVersion": "", "VpcID": "", "SubnetID": "", "ServiceControllerImage": "hkccr.ccs.tencentyun.com/tkeimages/service-controller:v2.3.1", "ServiceControllerVersion": "v2.3.1", "ServiceAvailableReplicas": 2, "ServiceExpectReplicas": 2, "ServiceArgs": "/service-controller;--backend-quota=0;--clusterName=cls-gkohai36;--kubeconfig=/root/.kube/config;--listener-quota=0;--reuse-flag=false", "ServiceImagePullPolicy": "Always", "ServiceResource": "&ResourceRequirements{Limits:ResourceList{cpu: {{200 -3} {<nil>} 200m DecimalSI},memory: {{536870912 0} {<nil>} 536870912 DecimalSI},},Requests:ResourceList{cpu: {{30 -3} {<nil>} 30m DecimalSI},memory: {{67108864 0} {<nil>} 67108864 DecimalSI},},}", "ServiceConfigMap": "LOADBALANCER_CRD_SUPPORT:true;REUSE_LOADBALANCER:false;VERSION:v2.3.1", "IngressControllerImage": "hkccr.ccs.tencentyun.com/tkeimages/ingress-controller:v2.2.3", "IngressControllerVersion": "v2.2.3", "IngressAvailableReplicas": 1, "IngressExpectReplicas": 1, "IngressArgs": "--cluster-name=cls-gkohai36", "IngressImagePullPolicy": "Always", "IngressResource": "&ResourceRequirements{Limits:ResourceList{},Requests:ResourceList{},}", "IngressConfigMap": "LOADBALANCER_CRD_SUPPORT:true;SILENT_START:false;VERSION:v2.2.6"}