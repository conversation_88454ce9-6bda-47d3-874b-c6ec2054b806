{"ClusterName": "即刻职达-tke-dev", "ClusterID": "cls-qgrg0pcj", "ClusterRegion": "ap-beijing", "ClusterType": "tke", "AppID": 1312933560, "Description": "", "MetaClusterID": "cls-0lk56led", "State": "", "TotalCLBCount": 6, "TotalServiceCount": 59, "TotalIngressCount": 3, "TotalTKEServiceConfigCount": 2, "RiskService": null, "RiskIngress": null, "ServiceCIDR": "", "NetworkType": "", "KubeProxyMode": "", "K8SVersion": "", "VpcID": "", "SubnetID": "", "ServiceControllerImage": "ccr.ccs.tencentyun.com/tkeimages/service-controller:v2.5.2", "ServiceControllerVersion": "v2.5.2", "ServiceAvailableReplicas": 2, "ServiceExpectReplicas": 2, "ServiceArgs": "/service-controller;--backend-quota=0;--clusterName=cls-qgrg0pcj;--kubeconfig=/root/.kube/config;--listener-quota=0", "ServiceImagePullPolicy": "Always", "ServiceResource": "&ResourceRequirements{Limits:ResourceList{cpu: {{200 -3} {<nil>} 200m DecimalSI},memory: {{536870912 0} {<nil>} 536870912 DecimalSI},},Requests:ResourceList{cpu: {{30 -3} {<nil>} 30m DecimalSI},memory: {{67108864 0} {<nil>} 67108864 DecimalSI},},}", "ServiceConfigMap": "EnableNodeGracefulDeletion:false;GlobalRouteDirectAccess:true;LOADBALANCER_CRD_SUPPORT:true;REUSE_LOADBALANCER:true;VERSION:v2.5.2", "IngressControllerImage": "ccr.ccs.tencentyun.com/tkeimages/ingress-controller:v2.5.2", "IngressControllerVersion": "v2.5.2", "IngressAvailableReplicas": 2, "IngressExpectReplicas": 2, "IngressArgs": "--cluster-name=cls-qgrg0pcj", "IngressImagePullPolicy": "Always", "IngressResource": "&ResourceRequirements{Limits:ResourceList{cpu: {{4 0} {<nil>} 4 DecimalSI},memory: {{8589934592 0} {<nil>}  BinarySI},},Requests:ResourceList{cpu: {{50 -3} {<nil>} 50m DecimalSI},memory: {{104857600 0} {<nil>} 100Mi BinarySI},},}", "IngressConfigMap": "LOADBALANCER_CRD_SUPPORT:true;SILENT_START:false;VERSION:v2.5.2"}