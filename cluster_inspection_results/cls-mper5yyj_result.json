{"ClusterName": "lookus", "ClusterID": "cls-mper5yyj", "ClusterRegion": "ap-shanghai", "ClusterType": "tke", "AppID": 1310493810, "Description": "", "MetaClusterID": "cls-nppxz1ip", "State": "", "TotalCLBCount": 6, "TotalServiceCount": 16, "TotalIngressCount": 5, "TotalTKEServiceConfigCount": 0, "RiskService": null, "RiskIngress": null, "ServiceCIDR": "", "NetworkType": "", "KubeProxyMode": "", "K8SVersion": "", "VpcID": "", "SubnetID": "", "ServiceControllerImage": "ccr.ccs.tencentyun.com/tkeimages/service-controller:v2.5.0", "ServiceControllerVersion": "v2.5.0", "ServiceAvailableReplicas": 2, "ServiceExpectReplicas": 2, "ServiceArgs": "/service-controller;--backend-quota=0;--clusterName=cls-mper5yyj;--kubeconfig=/root/.kube/config;--listener-quota=0", "ServiceImagePullPolicy": "IfNotPresent", "ServiceResource": "&ResourceRequirements{Limits:ResourceList{cpu: {{200 -3} {<nil>} 200m DecimalSI},memory: {{536870912 0} {<nil>} 536870912 DecimalSI},},Requests:ResourceList{cpu: {{30 -3} {<nil>} 30m DecimalSI},memory: {{52428800 0} {<nil>} 52428800 DecimalSI},},}", "ServiceConfigMap": "LOADBALANCER_CRD_SUPPORT:true;REUSE_LOADBALANCER:true;VERSION:v2.5.0;EnableNodeGracefulDeletion:false;GlobalRouteDirectAccess:true", "IngressControllerImage": "ccr.ccs.tencentyun.com/tkeimages/ingress-controller:v2.5.0", "IngressControllerVersion": "v2.5.0", "IngressAvailableReplicas": 2, "IngressExpectReplicas": 2, "IngressArgs": "--cluster-name=cls-mper5yyj", "IngressImagePullPolicy": "Always", "IngressResource": "&ResourceRequirements{Limits:ResourceList{cpu: {{4 0} {<nil>} 4 DecimalSI},memory: {{8589934592 0} {<nil>}  BinarySI},},Requests:ResourceList{cpu: {{50 -3} {<nil>} 50m DecimalSI},memory: {{104857600 0} {<nil>} 100Mi BinarySI},},}", "IngressConfigMap": "LOADBALANCER_CRD_SUPPORT:true;SILENT_START:false;VERSION:v2.5.0"}