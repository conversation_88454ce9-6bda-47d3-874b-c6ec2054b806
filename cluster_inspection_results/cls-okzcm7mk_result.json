{"ClusterName": "beta_k8s", "ClusterID": "cls-okzcm7mk", "ClusterRegion": "ap-guangzhou", "ClusterType": "tke", "AppID": 1317941377, "Description": "", "MetaClusterID": "cls-1s75hexc", "State": "", "TotalCLBCount": 55, "TotalServiceCount": 535, "TotalIngressCount": 61, "TotalTKEServiceConfigCount": 0, "RiskService": null, "RiskIngress": null, "ServiceCIDR": "", "NetworkType": "", "KubeProxyMode": "", "K8SVersion": "", "VpcID": "", "SubnetID": "", "ServiceControllerImage": "ccr.ccs.tencentyun.com/tkeimages/service-controller:v2.3.2", "ServiceControllerVersion": "v2.3.2", "ServiceAvailableReplicas": 2, "ServiceExpectReplicas": 2, "ServiceArgs": "/service-controller;--backend-quota=0;--clusterName=cls-okzcm7mk;--kubeconfig=/root/.kube/config;--listener-quota=0;--reuse-flag=false", "ServiceImagePullPolicy": "Always", "ServiceResource": "&ResourceRequirements{Limits:ResourceList{cpu: {{200 -3} {<nil>} 200m DecimalSI},memory: {{536870912 0} {<nil>} 536870912 DecimalSI},},Requests:ResourceList{cpu: {{30 -3} {<nil>} 30m DecimalSI},memory: {{67108864 0} {<nil>} 67108864 DecimalSI},},}", "ServiceConfigMap": "REUSE_LOADBALANCER:false;VERSION:v2.3.2;GlobalRouteDirectAccess:true;LOADBALANCER_CRD_SUPPORT:true", "IngressControllerImage": "ccr.ccs.tencentyun.com/tkeimages/ingress-controller:v2.2.5", "IngressControllerVersion": "v2.2.5", "IngressAvailableReplicas": 2, "IngressExpectReplicas": 2, "IngressArgs": "--cluster-name=cls-okzcm7mk", "IngressImagePullPolicy": "Always", "IngressResource": "&ResourceRequirements{Limits:ResourceList{cpu: {{4 0} {<nil>} 4 DecimalSI},memory: {{8589934592 0} {<nil>}  BinarySI},},Requests:ResourceList{cpu: {{50 -3} {<nil>} 50m DecimalSI},memory: {{104857600 0} {<nil>} 100Mi BinarySI},},}", "IngressConfigMap": "LOADBALANCER_CRD_SUPPORT:true;SILENT_START:false;VERSION:v2.2.6"}