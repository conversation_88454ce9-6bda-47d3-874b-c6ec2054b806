{"ClusterName": "dawa-k8s", "ClusterID": "cls-h05ox5x2", "ClusterRegion": "eu-frankfurt", "ClusterType": "tke", "AppID": 1317941377, "Description": "", "MetaClusterID": "cls-5t7brmqa", "State": "", "TotalCLBCount": 4, "TotalServiceCount": 20, "TotalIngressCount": 3, "TotalTKEServiceConfigCount": 0, "RiskService": null, "RiskIngress": null, "ServiceCIDR": "", "NetworkType": "", "KubeProxyMode": "", "K8SVersion": "", "VpcID": "", "SubnetID": "", "ServiceControllerImage": "", "ServiceControllerVersion": "", "ServiceAvailableReplicas": 0, "ServiceExpectReplicas": 0, "ServiceArgs": "", "ServiceImagePullPolicy": "", "ServiceResource": "", "ServiceConfigMap": "LOADBALANCER_CRD_SUPPORT:true;REUSE_LOADBALANCER:true;VERSION:v2.7.0;EnableIngressController:true;EnableNodeGracefulDeletion:false;GlobalRouteDirectAccess:true", "IngressControllerImage": "", "IngressControllerVersion": "", "IngressAvailableReplicas": 0, "IngressExpectReplicas": 0, "IngressArgs": "", "IngressImagePullPolicy": "", "IngressResource": "", "IngressConfigMap": ""}