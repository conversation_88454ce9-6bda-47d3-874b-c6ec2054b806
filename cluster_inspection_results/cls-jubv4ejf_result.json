{"ClusterName": "数据部-pub", "ClusterID": "cls-jubv4ejf", "ClusterRegion": "ap-shanghai", "ClusterType": "tke", "AppID": 1256526072, "Description": "", "MetaClusterID": "cls-gofhtn3t", "State": "", "TotalCLBCount": 25, "TotalServiceCount": 176, "TotalIngressCount": 74, "TotalTKEServiceConfigCount": 0, "ServiceCIDR": "", "NetworkType": "", "KubeProxyMode": "", "K8SVersion": "", "VpcID": "", "SubnetID": "", "ServiceControllerImage": "ccr.ccs.tencentyun.com/tkeimages/service-controller:v2.3.1", "ServiceControllerVersion": "v2.3.1", "ServiceAvailableReplicas": 2, "ServiceExpectReplicas": 2, "ServiceArgs": "/service-controller;--backend-quota=0;--clusterName=cls-jubv4ejf;--kubeconfig=/root/.kube/config;--listener-quota=0;--reuse-flag=false", "ServiceImagePullPolicy": "Always", "ServiceResource": "&ResourceRequirements{Limits:ResourceList{cpu: {{200 -3} {<nil>} 200m DecimalSI},memory: {{536870912 0} {<nil>} 536870912 DecimalSI},},Requests:ResourceList{cpu: {{30 -3} {<nil>} 30m DecimalSI},memory: {{67108864 0} {<nil>} 67108864 DecimalSI},},}", "ServiceConfigMap": "VERSION:v2.3.1;LOADBALANCER_CRD_SUPPORT:true;REUSE_LOADBALANCER:false", "IngressControllerImage": "ccr.ccs.tencentyun.com/tkeimages/ingress-controller:v2.2.3", "IngressControllerVersion": "v2.2.3", "IngressAvailableReplicas": 2, "IngressExpectReplicas": 2, "IngressArgs": "--cluster-name=cls-jubv4ejf", "IngressImagePullPolicy": "Always", "IngressResource": "&ResourceRequirements{Limits:ResourceList{},Requests:ResourceList{},}", "IngressConfigMap": "LOADBALANCER_CRD_SUPPORT:true;SILENT_START:false;VERSION:v2.2.6"}