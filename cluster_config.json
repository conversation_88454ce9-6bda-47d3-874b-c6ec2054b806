{"api_config": {"base_url": "http://kateway.woa.com/inspection/cluster/info/get", "filter": "clb", "refresh_cache": "true", "default_timeout": 30, "default_parallel": 5, "default_delay": 0.1}, "cluster_groups": {"production": ["cls-pnocuy7o", "cls-8s9918je", "cls-mteeijug", "cls-2<PERSON>vit7", "cls-0sf7xq8h"], "staging": ["cls-e6antxq2", "cls-019ctobc", "cls-c3mtb8tw", "cls-okzcm7mk"], "development": ["cls-0fvw4ql7", "cls-m29ot7ga", "cls-kbj8dces", "cls-97tqtjxu"], "all": ["cls-pnocuy7o", "cls-8s9918je", "cls-mteeijug", "cls-2<PERSON>vit7", "cls-0sf7xq8h", "cls-e6antxq2", "cls-019ctobc", "cls-c3mtb8tw", "cls-okzcm7mk", "cls-0fvw4ql7", "cls-m29ot7ga", "cls-kbj8dces", "cls-97tqtjxu", "cls-ky7xdrzx", "cls-glmxm5i6", "cls-nsftw5og", "cls-irbazn32", "cls-242vz9pu", "cls-65s6l2y8", "cls-h05ox5x2", "cls-dazrr6e2", "cls-isccrwql", "cls-7gvwytdd", "cls-mi9lwsrb", "cls-371qkhf6", "cls-la3q70cx", "cls-mper5yyj", "cls-3c4eezcs", "cls-jcb5w9dm", "cls-rjzry0t9", "cls-13j49wqb", "cls-quti290e", "cls-5ycseuoq", "cls-bg6nw5ip", "cls-mqmg9agh", "cls-jubv4ejf", "cls-2bvoyng8", "cls-jrb1523h", "cls-gkohai36", "cls-qvk25nxu", "cls-43begdw1", "cls-qgrg0pcj", "cls-hs3tnhee", "cls-n4qoxw50", "cls-4aznd6j3", "cls-2300v0c4"]}, "analysis_config": {"risk_scoring": {"ingress_weight": 2, "service_weight": 1, "high_risk_threshold": 5}, "reporting": {"max_top_clusters": 10, "max_top_namespaces": 10, "max_cross_cluster_patterns": 10}}}