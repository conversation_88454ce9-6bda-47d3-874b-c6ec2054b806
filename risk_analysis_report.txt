================================================================================
CLUSTER RISK ANALYSIS REPORT
================================================================================
Generated at: 2025年 6月24日 星期二 23时37分01秒 CST

EXECUTIVE SUMMARY
----------------------------------------
Total clusters analyzed: 46
Clusters with RiskIngress: 7 (15.2%)
Clusters with RiskService: 5 (10.9%)
Total risk entries: 18
  - RiskIngress: 13
  - RiskService: 5

TOP RISK CLUSTERS
----------------------------------------
 1. cls-m29ot7ga (Score: 14, Ingress: 7, Service: 0)
 2. cls-019ctobc (Score: 2, Ingress: 1, Service: 0)
 3. cls-glmxm5i6 (Score: 2, Ingress: 1, Service: 0)
 4. cls-65s6l2y8 (Score: 2, Ingress: 1, Service: 0)
 5. cls-dazrr6e2 (Score: 2, Ingress: 1, Service: 0)
 6. cls-371qkhf6 (Score: 2, Ingress: 1, Service: 0)
 7. cls-mi9lwsrb (Score: 2, Ingress: 1, Service: 0)
 8. cls-pnocuy7o (Score: 1, Ingress: 0, Service: 1)
 9. cls-8s9918je (Score: 1, Ingress: 0, Service: 1)
10. cls-0sf7xq8h (Score: 1, Ingress: 0, Service: 1)

CROSS-CLUSTER PATTERNS
----------------------------------------
• INGRESS: cnb-prod-a/lbid (appears in 1 clusters, 7 total)

RECOMMENDATIONS
----------------------------------------
1. [HIGH] Cluster Risk
   Found 1 high-risk clusters with risk score >= 5
   Action: Review and fix pass-to-target configurations in these clusters

2. [MEDIUM] Cross-Cluster Pattern
   Found 1 resource patterns appearing across multiple clusters
   Action: Consider standardizing configurations across clusters
