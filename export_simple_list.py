#!/usr/bin/env python3
"""
Export simple risk entries list with / separated columns
Usage: python3 export_simple_list.py [aggregated_risk_analysis.json]
"""

import json
import sys
import os
import argparse

def load_aggregated_data(file_path):
    """Load aggregated risk analysis data"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"Error: File {file_path} not found")
        return None
    except json.JSONDecodeError:
        print(f"Error: Invalid JSON in {file_path}")
        return None

def parse_risk_entry(entry_string):
    """Parse risk entry string into components"""
    # Format: clusterID/{cluster_id}/namespace/{namespace}/name{resource_name}/lbid/{lb_id}
    parts = entry_string.split('/')
    
    if len(parts) >= 7:
        cluster_id = parts[1]
        namespace = parts[3]
        
        # Extract resource name - it comes after 'name' without a separator
        name_part = parts[4]  # This contains 'name{resource_name}'
        if name_part.startswith('name'):
            resource_name = name_part[4:]  # Remove 'name' prefix
        else:
            resource_name = name_part
            
        lb_id = parts[6]
        
        return cluster_id, namespace, resource_name, lb_id
    else:
        return 'unknown', 'unknown', 'unknown', 'unknown'

def export_simple_lists(data, output_dir):
    """Export simple lists with / separated columns"""
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Export RiskIngress
    ingress_file = os.path.join(output_dir, 'risk_ingress_simple.txt')
    with open(ingress_file, 'w', encoding='utf-8') as f:
        f.write("# RiskIngress entries - Format: Type/Cluster_ID/Namespace/Resource_Name/LB_ID\n")
        for entry in data['risk_ingress']['all_entries']:
            cluster_id, namespace, resource_name, lb_id = parse_risk_entry(entry['entry'])
            f.write(f"Ingress/{cluster_id}/{namespace}/{resource_name}/{lb_id}\n")
    
    # Export RiskService
    service_file = os.path.join(output_dir, 'risk_service_simple.txt')
    with open(service_file, 'w', encoding='utf-8') as f:
        f.write("# RiskService entries - Format: Type/Cluster_ID/Namespace/Resource_Name/LB_ID\n")
        for entry in data['risk_service']['all_entries']:
            cluster_id, namespace, resource_name, lb_id = parse_risk_entry(entry['entry'])
            f.write(f"Service/{cluster_id}/{namespace}/{resource_name}/{lb_id}\n")
    
    # Export combined
    combined_file = os.path.join(output_dir, 'risk_all_simple.txt')
    with open(combined_file, 'w', encoding='utf-8') as f:
        f.write("# All risk entries - Format: Type/Cluster_ID/Namespace/Resource_Name/LB_ID\n")
        
        # Add RiskIngress entries
        for entry in data['risk_ingress']['all_entries']:
            cluster_id, namespace, resource_name, lb_id = parse_risk_entry(entry['entry'])
            f.write(f"Ingress/{cluster_id}/{namespace}/{resource_name}/{lb_id}\n")
        
        # Add RiskService entries
        for entry in data['risk_service']['all_entries']:
            cluster_id, namespace, resource_name, lb_id = parse_risk_entry(entry['entry'])
            f.write(f"Service/{cluster_id}/{namespace}/{resource_name}/{lb_id}\n")
    
    # Export only the parsed columns (no type prefix)
    columns_file = os.path.join(output_dir, 'risk_columns_only.txt')
    with open(columns_file, 'w', encoding='utf-8') as f:
        f.write("# Risk entries - Format: Cluster_ID/Namespace/Resource_Name/LB_ID\n")
        
        # Add RiskIngress entries
        for entry in data['risk_ingress']['all_entries']:
            cluster_id, namespace, resource_name, lb_id = parse_risk_entry(entry['entry'])
            f.write(f"{cluster_id}/{namespace}/{resource_name}/{lb_id}\n")
        
        # Add RiskService entries
        for entry in data['risk_service']['all_entries']:
            cluster_id, namespace, resource_name, lb_id = parse_risk_entry(entry['entry'])
            f.write(f"{cluster_id}/{namespace}/{resource_name}/{lb_id}\n")
    
    return len(data['risk_ingress']['all_entries']), len(data['risk_service']['all_entries'])

def main():
    parser = argparse.ArgumentParser(description='Export simple risk entries with / separated columns')
    parser.add_argument('input_file', nargs='?',
                       default='cluster_inspection_results/aggregated_risk_analysis.json',
                       help='Path to aggregated risk analysis JSON file')
    parser.add_argument('--output-dir', '-o',
                       default='risk_simple_export',
                       help='Output directory for exported files')
    
    args = parser.parse_args()
    
    # Load data
    print("Loading aggregated risk data...")
    data = load_aggregated_data(args.input_file)
    if not data:
        sys.exit(1)
    
    # Export simple lists
    print(f"Exporting simple lists to: {args.output_dir}")
    ingress_count, service_count = export_simple_lists(data, args.output_dir)
    
    print("\n" + "="*60)
    print("SIMPLE EXPORT COMPLETED")
    print("="*60)
    print(f"RiskIngress entries: {ingress_count}")
    print(f"RiskService entries: {service_count}")
    print(f"Total entries: {ingress_count + service_count}")
    print(f"\nFiles generated in: {args.output_dir}/")
    print("- risk_ingress_simple.txt  (Ingress/Cluster/Namespace/Resource/LB)")
    print("- risk_service_simple.txt  (Service/Cluster/Namespace/Resource/LB)")
    print("- risk_all_simple.txt      (Type/Cluster/Namespace/Resource/LB)")
    print("- risk_columns_only.txt    (Cluster/Namespace/Resource/LB)")
    print("\nEach line format: field1/field2/field3/field4/field5")

if __name__ == "__main__":
    main()
