# 批量集群巡检脚本

这些脚本用于批量调用集群巡检API，并聚合所有响应中的 `RiskIngress` 和 `RiskService` 数据。

## 脚本说明

### 1. Python脚本 (`batch_cluster_inspection.py`)

**功能特性：**
- 支持并发请求，提高处理速度
- 自动提取和聚合 `RiskIngress` 和 `RiskService` 数据
- 生成详细的风险分析报告
- 支持多种输出格式（JSON、文本摘要）
- 完善的错误处理和进度跟踪

**使用方法：**
```bash
# 基本使用
python3 batch_cluster_inspection.py

# 自定义参数
python3 batch_cluster_inspection.py --parallel 10 --timeout 60 --delay 0.2
```

**参数说明：**
- `--parallel, -p`: 并发请求数量（默认：5）
- `--timeout, -t`: 请求超时时间，秒（默认：30）
- `--delay, -d`: 请求间延迟时间，秒（默认：0.1）

### 2. Bash脚本 (`batch_cluster_inspection.sh`)

**功能特性：**
- 简单易用，无需额外依赖
- 基本的风险数据聚合功能
- 详细的执行日志
- 支持 jq 工具进行更好的JSON解析

**使用方法：**
```bash
chmod +x batch_cluster_inspection.sh
./batch_cluster_inspection.sh
```

## 输出文件说明

### 目录结构
```
cluster_inspection_results/
├── batch_execution.log                 # 执行日志
├── {cluster_id}_result.json           # 各集群的API响应
├── {cluster_id}_error.txt              # 错误信息（如有）
├── aggregated_risk_analysis.json      # 聚合的风险分析数据（Python版本）
├── risk_summary.txt                   # 风险摘要报告
├── aggregated_risk_ingress.txt        # 聚合的RiskIngress数据（Bash版本）
└── aggregated_risk_service.txt        # 聚合的RiskService数据（Bash版本）
```

### 聚合数据结构

#### Python版本输出 (`aggregated_risk_analysis.json`)
```json
{
  "summary": {
    "total_clusters": 46,
    "clusters_with_risk_ingress": 5,
    "clusters_with_risk_service": 8,
    "total_risk_ingress_count": 12,
    "total_risk_service_count": 15
  },
  "risk_ingress": {
    "all_entries": [...],
    "by_cluster": {...},
    "unique_entries": [...],
    "grouped_by_pattern": {...}
  },
  "risk_service": {
    "all_entries": [...],
    "by_cluster": {...},
    "unique_entries": [...],
    "grouped_by_pattern": {...}
  }
}
```

#### 风险条目格式
RiskIngress 和 RiskService 的条目格式为：
```
clusterID/{cluster_id}/namespace/{namespace}/name/{name}/lbid/{lb_id}
```

#### 分组模式
脚本会自动按照 `namespace/name` 模式对风险条目进行分组，便于识别跨集群的相同资源风险。

## API接口说明

**接口地址：** `http://kateway.woa.com/inspection/cluster/info/get`

**请求参数：**
- `clusterID`: 集群ID（必需）
- `filter`: 过滤器，固定为 "clb"
- `refreshCache`: 是否刷新缓存，固定为 "true"

**响应结构：**
```json
{
  "data": {
    "ClusterBaseStats": {
      "RiskIngress": ["entry1", "entry2", ...],
      "RiskService": ["entry1", "entry2", ...]
    }
  }
}
```

## 集群ID列表

脚本中包含以下集群ID（已去重）：
- cls-pnocuy7o, cls-8s9918je, cls-mteeijug, cls-2nyavit7
- cls-0sf7xq8h, cls-e6antxq2, cls-019ctobc, cls-c3mtb8tw
- cls-okzcm7mk, cls-0fvw4ql7, cls-m29ot7ga, cls-kbj8dces
- ... 等共46个集群

## 注意事项

1. **网络连接：** 确保能够访问 `kateway.woa.com` 域名
2. **并发控制：** 建议并发数不要设置过高，避免对服务器造成压力
3. **超时设置：** 根据网络情况调整超时时间
4. **依赖工具：** 
   - Python版本需要 `requests` 库
   - Bash版本建议安装 `jq` 工具以获得更好的JSON解析效果

## 故障排除

### 常见问题

1. **连接超时：** 增加 `--timeout` 参数值
2. **JSON解析错误：** 检查API响应格式是否正确
3. **权限问题：** 确保有访问API的权限
4. **文件权限：** 确保脚本有执行权限和写入权限

### 日志查看
所有执行日志都保存在 `cluster_inspection_results/batch_execution.log` 文件中，可以查看详细的执行过程和错误信息。

## 快速开始

### 一键执行（推荐）
```bash
# 使用默认设置运行完整流程
./run_batch_inspection.sh

# 自定义参数运行
./run_batch_inspection.sh --parallel 10 --timeout 60

# 查看帮助
./run_batch_inspection.sh --help
```

### 高级分析

#### 风险数据分析脚本 (`analyze_risk_data.py`)
```bash
# 分析聚合数据并生成报告
python3 analyze_risk_data.py

# 指定输入文件
python3 analyze_risk_data.py path/to/aggregated_risk_analysis.json

# 输出JSON格式的详细分析
python3 analyze_risk_data.py --json --output detailed_analysis.json
```

**分析功能：**
- 集群风险评分和排名
- 命名空间风险模式分析
- 跨集群资源模式识别
- 负载均衡器使用分析
- 自动生成修复建议

## 配置文件

### 集群配置 (`cluster_config.json`)
提供了示例配置文件，支持：
- 按环境分组的集群列表（生产、测试、开发）
- API配置参数
- 风险评分权重配置
- 报告生成参数

## 扩展功能

### 自定义集群列表
1. 编辑 `cluster_config.json` 文件
2. 或直接修改脚本中的 `CLUSTER_IDS` 数组

### 自定义API参数
修改脚本中的配置变量：
- `BASE_URL`: API基础地址
- `FILTER`: 过滤器参数
- `REFRESH_CACHE`: 缓存刷新参数

### 输出格式定制
可以修改聚合函数来自定义输出格式和分析维度。

### 风险评分定制
在 `analyze_risk_data.py` 中修改风险评分算法：
- 调整 Ingress 和 Service 的权重
- 自定义高风险阈值
- 添加新的风险指标
