============================================================
RISK ENTRIES EXPORT SUMMARY
============================================================
Export Date: 2025-06-24 23:43:02
Source File: aggregated_risk_analysis.json

EXPORT STATISTICS:
------------------------------
Total RiskIngress entries: 13
Total RiskService entries: 5
Total risk entries: 18
Total clusters analyzed: 46

GENERATED FILES:
------------------------------
• risk_ingress_details.csv - RiskIngress entries in CSV format
• risk_ingress_details.txt - RiskIngress entries in tab-separated format
• risk_service_details.csv - RiskService entries in CSV format
• risk_service_details.txt - RiskService entries in tab-separated format
• risk_all_details.csv - Combined entries in CSV format
• risk_all_details.txt - Combined entries in tab-separated format
• export_summary.txt - This summary file

FILE FORMAT:
------------------------------
Columns: Type | Cluster_ID | Namespace | Resource_Name | LB_ID | Full_Entry
- Type: 'Ingress' or 'Service'
- Cluster_ID: Kubernetes cluster identifier
- Namespace: Kubernetes namespace
- Resource_Name: Name of the Ingress or Service resource
- LB_ID: Load balancer identifier
- Full_Entry: Complete original risk entry string

NAMESPACE BREAKDOWN:
------------------------------
auto-ai: 1 total (Ingress: 0, Service: 1)
b2b-prod: 1 total (Ingress: 1, Service: 0)
cnb-prod-a: 7 total (Ingress: 7, Service: 0)
code-intell: 1 total (Ingress: 1, Service: 0)
default: 1 total (Ingress: 1, Service: 0)
itest: 1 total (Ingress: 0, Service: 1)
nfv-manager-system: 1 total (Ingress: 0, Service: 1)
prod: 1 total (Ingress: 1, Service: 0)
tdmq-system: 1 total (Ingress: 0, Service: 1)
testing: 1 total (Ingress: 0, Service: 1)
union-pay-data: 1 total (Ingress: 1, Service: 0)
vista-womovie-prod: 1 total (Ingress: 1, Service: 0)
