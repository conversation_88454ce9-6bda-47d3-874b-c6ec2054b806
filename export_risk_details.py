#!/usr/bin/env python3
"""
Export detailed risk entries with / separated columns
Usage: python3 export_risk_details.py [aggregated_risk_analysis.json]
"""

import json
import sys
import os
import csv
import argparse
from datetime import datetime

def load_aggregated_data(file_path):
    """Load aggregated risk analysis data"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"Error: File {file_path} not found")
        return None
    except json.JSONDecodeError:
        print(f"Error: Invalid JSON in {file_path}")
        return None

def parse_risk_entry(entry_string):
    """Parse risk entry string into components"""
    # Format: clusterID/{cluster_id}/namespace/{namespace}/name{resource_name}/lbid/{lb_id}
    # Example: clusterID/cls-019ctobc/namespace/b2b-prod/nameingress/lbid/lb-89100oz0
    # Parts:   [0]clusterID [1]cluster_id [2]namespace [3]namespace_name [4]name+resource [5]lbid [6]lb_id

    parts = entry_string.split('/')

    if len(parts) >= 7:
        cluster_id = parts[1]
        namespace = parts[3]

        # Extract resource name - it comes after 'name' without a separator
        name_part = parts[4]  # This contains 'name{resource_name}'
        if name_part.startswith('name'):
            resource_name = name_part[4:]  # Remove 'name' prefix
        else:
            resource_name = name_part

        # Extract LB ID
        lb_id = parts[6]

        return {
            'cluster_id': cluster_id,
            'namespace': namespace,
            'resource_name': resource_name,
            'lb_id': lb_id,
            'full_entry': entry_string
        }
    else:
        # Handle malformed entries - try to extract what we can
        cluster_id = 'unknown'
        namespace = 'unknown'
        resource_name = 'unknown'
        lb_id = 'unknown'

        if len(parts) > 1:
            cluster_id = parts[1]
        if len(parts) > 3:
            namespace = parts[3]
        if len(parts) > 4:
            name_part = parts[4]
            if name_part.startswith('name'):
                resource_name = name_part[4:]
            else:
                resource_name = name_part
        if len(parts) > 6:
            lb_id = parts[6]

        return {
            'cluster_id': cluster_id,
            'namespace': namespace,
            'resource_name': resource_name,
            'lb_id': lb_id,
            'full_entry': entry_string
        }

def export_risk_ingress(data, output_dir):
    """Export RiskIngress data to CSV and TXT files"""
    ingress_data = []
    
    # Process all RiskIngress entries
    for entry in data['risk_ingress']['all_entries']:
        parsed = parse_risk_entry(entry['entry'])
        parsed['source_cluster'] = entry['cluster_id']
        parsed['type'] = 'Ingress'
        ingress_data.append(parsed)
    
    # Sort by cluster_id, then namespace, then resource_name
    ingress_data.sort(key=lambda x: (x['cluster_id'], x['namespace'], x['resource_name']))
    
    # Export to CSV
    csv_file = os.path.join(output_dir, 'risk_ingress_details.csv')
    with open(csv_file, 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        writer.writerow(['Type', 'Cluster_ID', 'Namespace', 'Resource_Name', 'LB_ID', 'Full_Entry'])
        
        for item in ingress_data:
            writer.writerow([
                item['type'],
                item['cluster_id'],
                item['namespace'], 
                item['resource_name'],
                item['lb_id'],
                item['full_entry']
            ])
    
    # Export to TXT (tab-separated)
    txt_file = os.path.join(output_dir, 'risk_ingress_details.txt')
    with open(txt_file, 'w', encoding='utf-8') as f:
        f.write("Type\tCluster_ID\tNamespace\tResource_Name\tLB_ID\tFull_Entry\n")
        
        for item in ingress_data:
            f.write(f"{item['type']}\t{item['cluster_id']}\t{item['namespace']}\t{item['resource_name']}\t{item['lb_id']}\t{item['full_entry']}\n")
    
    return len(ingress_data), csv_file, txt_file

def export_risk_service(data, output_dir):
    """Export RiskService data to CSV and TXT files"""
    service_data = []
    
    # Process all RiskService entries
    for entry in data['risk_service']['all_entries']:
        parsed = parse_risk_entry(entry['entry'])
        parsed['source_cluster'] = entry['cluster_id']
        parsed['type'] = 'Service'
        service_data.append(parsed)
    
    # Sort by cluster_id, then namespace, then resource_name
    service_data.sort(key=lambda x: (x['cluster_id'], x['namespace'], x['resource_name']))
    
    # Export to CSV
    csv_file = os.path.join(output_dir, 'risk_service_details.csv')
    with open(csv_file, 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        writer.writerow(['Type', 'Cluster_ID', 'Namespace', 'Resource_Name', 'LB_ID', 'Full_Entry'])
        
        for item in service_data:
            writer.writerow([
                item['type'],
                item['cluster_id'],
                item['namespace'],
                item['resource_name'], 
                item['lb_id'],
                item['full_entry']
            ])
    
    # Export to TXT (tab-separated)
    txt_file = os.path.join(output_dir, 'risk_service_details.txt')
    with open(txt_file, 'w', encoding='utf-8') as f:
        f.write("Type\tCluster_ID\tNamespace\tResource_Name\tLB_ID\tFull_Entry\n")
        
        for item in service_data:
            f.write(f"{item['type']}\t{item['cluster_id']}\t{item['namespace']}\t{item['resource_name']}\t{item['lb_id']}\t{item['full_entry']}\n")
    
    return len(service_data), csv_file, txt_file

def export_combined_data(data, output_dir):
    """Export combined RiskIngress and RiskService data"""
    combined_data = []
    
    # Process RiskIngress entries
    for entry in data['risk_ingress']['all_entries']:
        parsed = parse_risk_entry(entry['entry'])
        parsed['source_cluster'] = entry['cluster_id']
        parsed['type'] = 'Ingress'
        combined_data.append(parsed)
    
    # Process RiskService entries  
    for entry in data['risk_service']['all_entries']:
        parsed = parse_risk_entry(entry['entry'])
        parsed['source_cluster'] = entry['cluster_id']
        parsed['type'] = 'Service'
        combined_data.append(parsed)
    
    # Sort by type, then cluster_id, then namespace, then resource_name
    combined_data.sort(key=lambda x: (x['type'], x['cluster_id'], x['namespace'], x['resource_name']))
    
    # Export to CSV
    csv_file = os.path.join(output_dir, 'risk_all_details.csv')
    with open(csv_file, 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        writer.writerow(['Type', 'Cluster_ID', 'Namespace', 'Resource_Name', 'LB_ID', 'Full_Entry'])
        
        for item in combined_data:
            writer.writerow([
                item['type'],
                item['cluster_id'],
                item['namespace'],
                item['resource_name'],
                item['lb_id'], 
                item['full_entry']
            ])
    
    # Export to TXT (tab-separated)
    txt_file = os.path.join(output_dir, 'risk_all_details.txt')
    with open(txt_file, 'w', encoding='utf-8') as f:
        f.write("Type\tCluster_ID\tNamespace\tResource_Name\tLB_ID\tFull_Entry\n")
        
        for item in combined_data:
            f.write(f"{item['type']}\t{item['cluster_id']}\t{item['namespace']}\t{item['resource_name']}\t{item['lb_id']}\t{item['full_entry']}\n")
    
    return len(combined_data), csv_file, txt_file

def generate_summary_report(data, output_dir, ingress_count, service_count, total_count):
    """Generate a summary report of the export"""
    summary_file = os.path.join(output_dir, 'export_summary.txt')
    
    with open(summary_file, 'w', encoding='utf-8') as f:
        f.write("=" * 60 + "\n")
        f.write("RISK ENTRIES EXPORT SUMMARY\n")
        f.write("=" * 60 + "\n")
        f.write(f"Export Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"Source File: aggregated_risk_analysis.json\n\n")
        
        f.write("EXPORT STATISTICS:\n")
        f.write("-" * 30 + "\n")
        f.write(f"Total RiskIngress entries: {ingress_count}\n")
        f.write(f"Total RiskService entries: {service_count}\n")
        f.write(f"Total risk entries: {total_count}\n")
        f.write(f"Total clusters analyzed: {data['summary']['total_clusters']}\n\n")
        
        f.write("GENERATED FILES:\n")
        f.write("-" * 30 + "\n")
        f.write("• risk_ingress_details.csv - RiskIngress entries in CSV format\n")
        f.write("• risk_ingress_details.txt - RiskIngress entries in tab-separated format\n")
        f.write("• risk_service_details.csv - RiskService entries in CSV format\n")
        f.write("• risk_service_details.txt - RiskService entries in tab-separated format\n")
        f.write("• risk_all_details.csv - Combined entries in CSV format\n")
        f.write("• risk_all_details.txt - Combined entries in tab-separated format\n")
        f.write("• export_summary.txt - This summary file\n\n")
        
        f.write("FILE FORMAT:\n")
        f.write("-" * 30 + "\n")
        f.write("Columns: Type | Cluster_ID | Namespace | Resource_Name | LB_ID | Full_Entry\n")
        f.write("- Type: 'Ingress' or 'Service'\n")
        f.write("- Cluster_ID: Kubernetes cluster identifier\n")
        f.write("- Namespace: Kubernetes namespace\n")
        f.write("- Resource_Name: Name of the Ingress or Service resource\n")
        f.write("- LB_ID: Load balancer identifier\n")
        f.write("- Full_Entry: Complete original risk entry string\n\n")
        
        # Add namespace breakdown
        namespace_stats = {}
        for entry in data['risk_ingress']['all_entries']:
            parsed = parse_risk_entry(entry['entry'])
            ns = parsed['namespace']
            if ns not in namespace_stats:
                namespace_stats[ns] = {'ingress': 0, 'service': 0}
            namespace_stats[ns]['ingress'] += 1
            
        for entry in data['risk_service']['all_entries']:
            parsed = parse_risk_entry(entry['entry'])
            ns = parsed['namespace']
            if ns not in namespace_stats:
                namespace_stats[ns] = {'ingress': 0, 'service': 0}
            namespace_stats[ns]['service'] += 1
        
        f.write("NAMESPACE BREAKDOWN:\n")
        f.write("-" * 30 + "\n")
        for ns, counts in sorted(namespace_stats.items()):
            total_ns = counts['ingress'] + counts['service']
            f.write(f"{ns}: {total_ns} total (Ingress: {counts['ingress']}, Service: {counts['service']})\n")
    
    return summary_file

def main():
    parser = argparse.ArgumentParser(description='Export detailed risk entries with / separated columns')
    parser.add_argument('input_file', nargs='?',
                       default='cluster_inspection_results/aggregated_risk_analysis.json',
                       help='Path to aggregated risk analysis JSON file')
    parser.add_argument('--output-dir', '-o',
                       default='risk_details_export',
                       help='Output directory for exported files')
    parser.add_argument('--format', choices=['csv', 'txt', 'both'],
                       default='both',
                       help='Output format (default: both)')
    
    args = parser.parse_args()
    
    # Load data
    print("Loading aggregated risk data...")
    data = load_aggregated_data(args.input_file)
    if not data:
        sys.exit(1)
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    print(f"Output directory: {args.output_dir}")
    
    # Export RiskIngress data
    print("Exporting RiskIngress data...")
    ingress_count, ingress_csv, ingress_txt = export_risk_ingress(data, args.output_dir)
    print(f"  Exported {ingress_count} RiskIngress entries")
    
    # Export RiskService data
    print("Exporting RiskService data...")
    service_count, service_csv, service_txt = export_risk_service(data, args.output_dir)
    print(f"  Exported {service_count} RiskService entries")
    
    # Export combined data
    print("Exporting combined data...")
    total_count, combined_csv, combined_txt = export_combined_data(data, args.output_dir)
    print(f"  Exported {total_count} total risk entries")
    
    # Generate summary
    summary_file = generate_summary_report(data, args.output_dir, ingress_count, service_count, total_count)
    
    print("\n" + "="*60)
    print("EXPORT COMPLETED SUCCESSFULLY")
    print("="*60)
    print(f"RiskIngress entries: {ingress_count}")
    print(f"RiskService entries: {service_count}")
    print(f"Total entries: {total_count}")
    print(f"\nFiles generated in: {args.output_dir}/")
    print("- risk_ingress_details.csv/txt")
    print("- risk_service_details.csv/txt") 
    print("- risk_all_details.csv/txt")
    print("- export_summary.txt")
    print("\nColumn format: Type | Cluster_ID | Namespace | Resource_Name | LB_ID | Full_Entry")

if __name__ == "__main__":
    main()
