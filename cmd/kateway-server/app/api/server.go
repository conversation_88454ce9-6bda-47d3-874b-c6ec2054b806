package api

import (
	"context"
	"fmt"
	"log"
	"net"
	"net/http"
	"reflect"
	"regexp"
	"strings"
	"time"

	"github.com/gosuri/uitable"
	"github.com/jinzhu/copier"
	"github.com/samber/lo"
	"github.com/spf13/cast"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/json"
	"google.golang.org/grpc"
	"google.golang.org/grpc/reflection"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/tools/cache"
	"k8s.io/klog/v2"
	"sigs.k8s.io/yaml"

	"git.woa.com/kateway/kateway-server/api/kateway"
	"git.woa.com/kateway/kateway-server/api/pb"
	"git.woa.com/kateway/kateway-server/cmd/kateway-server/app/config"
	services "git.woa.com/kateway/kateway-server/cmd/kops/kops/service"
	"git.woa.com/kateway/kateway-server/cmd/kops/kops/service/controller"
	"git.woa.com/kateway/kateway-server/pkg/alarm"
	"git.woa.com/kateway/kateway-server/pkg/image"
	"git.woa.com/kateway/kateway-server/pkg/service/clb"
	"git.woa.com/kateway/kateway-server/pkg/task"
	"git.woa.com/kateway/kateway-server/pkg/task/model"
	"git.woa.com/kateway/kateway-server/pkg/tmp/zhiyan"
)

type Server struct {
	version string
	kateway.UnimplementedKatewayServer
	starShip *StarShipServer
}

func NewServer() *Server {
	return &Server{
		version:  config.Get().KOPS.Version,
		starShip: NewStarShipServer(),
	}
}

func (s *Server) Run() {
	klog.InfoS("启动 kateway grpc 服务", "version:", s.version)

	lis, err := net.Listen("tcp", ":443")
	if err != nil {
		log.Fatal(err, "failed to listen")
		return
	}
	grpcServer := grpc.NewServer()
	reflection.Register(grpcServer)
	kateway.RegisterKatewayServer(grpcServer, s)
	pb.RegisterTPTaskEngineServer(grpcServer, s.starShip)
	if err := grpcServer.Serve(lis); err != nil {
		log.Fatal(err, "failed to serve")
		return
	}
}

func (s *Server) CheckUpgrade(ctx context.Context, req *kateway.CheckUpgradeRequest) (*kateway.CheckUpgradeResponse, error) {
	if req.GetVersion() == s.version {
		return nil, nil
	}

	return &kateway.CheckUpgradeResponse{
		Version: s.version,
		Url:     fmt.Sprintf("https://mirrors.tencent.com/repository/generic/kateway/kops/%s/%s/kops", s.version, req.GetOs()),
	}, nil
}

func (s *Server) GetUser(ctx context.Context, req *kateway.GetUserRequest) (*kateway.GetUserResponse, error) {
	user, err := services.Get().User().Get(ctx, req.GetQuery())
	if err != nil {
		return nil, err
	}

	toUser := &kateway.User{}
	err = copier.Copy(toUser, user)
	if err != nil {
		return nil, err
	}

	return &kateway.GetUserResponse{User: toUser}, nil
}

func (s *Server) GetCluster(ctx context.Context, req *kateway.GetClusterRequest) (*kateway.GetClusterResponse, error) {
	cluster, err := services.Get().Cluster().Get(ctx, req.GetQuery())
	if err != nil {
		return nil, err
	}
	user, err := services.Get().User().Get(ctx, cast.ToString(cluster.Appid))
	if err != nil {
		return nil, err
	}
	toUser := &kateway.User{}
	err = copier.Copy(toUser, user)
	if err != nil {
		return nil, err
	}

	toCluster := &kateway.Cluster{
		Id:   cluster.ClusterID,
		Type: cluster.Type,
		// Version:	cluster.Version,
		Name:   cluster.Name,
		Region: cluster.Region,
		// State:  cluster.State,
		// Vpc:		cluster.Vpc,
		// CreatedAt: cluster.CreatedAt,
		MetaCluster: cluster.MetaClusterID,
		User:        toUser,
	}

	return &kateway.GetClusterResponse{
		Cluster: toCluster,
	}, nil
}

func (s *Server) controller(ctx context.Context, target *kateway.Target) (*controller.Controller, error) {
	var (
		ctrl *controller.Instance
		err  error
	)
	if target.GetToken() != "" {
		klog.InfoS("使用token获取控制器", "cluster", target.GetCluster(), "user", target.GetUser(), "token", target.GetToken())
		ctrl, err = controller.GetByClusterIDWithIanvs(ctx, target.GetCluster(), target.GetUser(), target.GetToken())
	} else {
		klog.InfoS("使用老逻辑获取控制器", "cluster", target.GetCluster())
		ctrl, err = controller.GetByClusterID(ctx, target.GetCluster())
	}
	if err != nil {
		return nil, err
	}

	switch target.GetName() {
	case "service":
		return ctrl.Service(), nil
	case "ingress":
		return ctrl.Ingress(), nil
	}

	return nil, fmt.Errorf("未找到对应的控制器")
}

func (s *Server) GetCLB(ctx context.Context, req *kateway.GetCLBRequest) (*kateway.GetCLBResponse, error) {
	lb, err := clb.Get(req.GetQuery())
	if err != nil {
		return nil, err
	}

	return &kateway.GetCLBResponse{
		Data: s.toString(lb),
	}, nil
}

func (s *Server) GetListeners(ctx context.Context, req *kateway.GetCLBRequest) (*kateway.GetCLBResponse, error) {
	lb, err := clb.GetListeners(req.GetQuery())
	if err != nil {
		return nil, err
	}

	return &kateway.GetCLBResponse{
		Data: s.toString(lb),
	}, nil
}

func (s *Server) GetBackends(ctx context.Context, req *kateway.GetCLBRequest) (*kateway.GetCLBResponse, error) {
	lb, err := clb.GetBackends(req.GetQuery())
	if err != nil {
		return nil, err
	}

	return &kateway.GetCLBResponse{
		Data: s.toString(lb),
	}, nil
}

func (s *Server) GetHealth(ctx context.Context, req *kateway.GetCLBRequest) (*kateway.GetCLBResponse, error) {
	lb, err := clb.GetHealth(req.GetQuery())
	if err != nil {
		return nil, err
	}

	return &kateway.GetCLBResponse{
		Data: s.toString(lb),
	}, nil
}

func (s *Server) GetPod(ctx context.Context, req *kateway.GetPodRequest) (*kateway.GetPodResponse, error) {
	ctrl, err := s.controller(ctx, req.GetTarget())
	if err != nil {
		return nil, err
	}

	pods, err := ctrl.ListPodToString(ctx, controller.ListPodsOptions{})
	if err != nil {
		return nil, fmt.Errorf("获取pod失败: %w", err)
	}

	resp := &kateway.GetPodResponse{
		Data: pods,
	}

	return resp, nil
}

func (s *Server) GetDeploy(ctx context.Context, req *kateway.GetDeployRequest) (*kateway.GetDeployResponse, error) {
	ctrl, err := s.controller(ctx, req.GetTarget())
	if err != nil {
		return nil, err
	}

	deploy, err := ctrl.GetDeployment(ctx, controller.GetDeploymentOptions{})
	if err != nil {
		return nil, err
	}

	deploy.ManagedFields = nil

	resp := &kateway.GetDeployResponse{
		Data: s.toString(deploy),
	}

	return resp, nil
}

func (s *Server) GetLeader(ctx context.Context, req *kateway.GetLeaderRequest) (*kateway.GetLeaderResponse, error) {
	ctrl, err := s.controller(ctx, req.GetTarget())
	if err != nil {
		return nil, err
	}

	pod, err := ctrl.GetLeaderPod(ctx)
	if err != nil {
		return nil, err
	}

	pod.ManagedFields = nil

	resp := &kateway.GetLeaderResponse{
		Data: s.toString(pod),
	}

	return resp, nil
}

func (s *Server) GetConfig(ctx context.Context, req *kateway.GetConfigRequest) (*kateway.GetConfigResponse, error) {
	ctrl, err := s.controller(ctx, req.GetTarget())
	if err != nil {
		return nil, err
	}

	cm, err := ctrl.GetConfigMap(ctx)
	if err != nil {
		return nil, err
	}

	resp := &kateway.GetConfigResponse{
		Data: s.toString(cm),
	}

	return resp, nil
}

func (s *Server) Logs(ctx context.Context, req *kateway.LogsRequest) (*kateway.LogsResponse, error) {
	ctrl, err := s.controller(ctx, req.GetTarget())
	if err != nil {
		return nil, err
	}

	pod, err := ctrl.GetLeaderPod(ctx)
	if err != nil {
		return nil, err
	}
	logs, err := ctrl.GetLogs(ctx, controller.PodLogOptions{
		PodLogOptions: toPodLogOptions(req.GetLogOptions()),
		PodName:       pod.Name})
	if err != nil {
		return nil, err
	}

	resp := &kateway.LogsResponse{
		Data: strings.Join(logs[pod.Name], "\n"),
	}

	return resp, nil
}

func toPodLogOptions(in *kateway.LogOptions) corev1.PodLogOptions {
	if in.SinceTime == "" {
		in.SinceTime = time.Now().Add(-24 * time.Hour).Format(time.RFC3339)
	}
	t, err := time.Parse(time.RFC3339, in.SinceTime)
	if err != nil {
		panic(err)
	}
	out := corev1.PodLogOptions{
		SinceTime: lo.ToPtr[metav1.Time](metav1.NewTime(t)),
	}

	return out
}

func (s *Server) YunAPI(ctx context.Context, req *kateway.YunAPIRequest) (*kateway.YunAPIResponse, error) {
	ctrl, err := s.controller(ctx, req.GetTarget())
	if err != nil {
		return nil, err
	}

	pattern := regexp.MustCompile(`APIRecord|APIError`)
	if req.GetQuery() != "" {
		namespace, name, err := cache.SplitMetaNamespaceKey(req.GetQuery())
		if err != nil {
			return nil, err
		}
		pattern = regexp.MustCompile(fmt.Sprintf(`(APIRecord|APIError).*Name=%s Namespace=%s`, name, namespace))
		if namespace == "" {
			pattern = regexp.MustCompile(fmt.Sprintf(`(APIRecord|APIError).*Name=%s`, name))
		}
	}
	klog.InfoS("yunapi", "pattern", pattern)
	pod, err := ctrl.GetLeaderPod(ctx)
	if err != nil {
		return nil, err
	}

	logs, err := ctrl.GetLogs(ctx, controller.PodLogOptions{
		PodLogOptions: toPodLogOptions(req.GetLogOptions()),
		PodName:       pod.Name})
	if err != nil {
		return nil, err
	}

	lines := lo.Filter(logs[pod.Name], func(item string, index int) bool {
		return pattern.MatchString(item)
	})
	for index, line := range lines {
		lines[index] = cleanLog(line)
	}
	resp := &kateway.YunAPIResponse{
		Data: strings.Join(lines, "\n"),
	}

	return resp, nil
}

func cleanLog(line string) string {
	re := regexp.MustCompile(`\s+\d+\s+pkg\s+domain/tencentapi/tencentapi\.go:\d+] APIRecord Kind=\w+ Name=\w+ Namespace=\w+`)
	line = re.ReplaceAllString(line, "")

	return line
}

func (s *Server) Admin(ctx context.Context, req *kateway.AdminRequest) (*kateway.AdminResponse, error) {
	ctrl, err := s.controller(ctx, req.GetTarget())
	if err != nil {
		return nil, err
	}

	data, err := ctrl.ExecLeaderAdmin(ctx, http.MethodGet, "/status", nil)
	if err != nil {
		return nil, err
	}
	resp := &kateway.AdminResponse{
		Data: string(data),
	}

	return resp, nil
}

func (s *Server) toString(object interface{}) string {
	metaObject, ok := object.(metav1.Object)
	if ok {
		metaObject.SetManagedFields(nil)
		object = metaObject
	}

	data, _ := json.Marshal(object)
	data, _ = yaml.JSONToYAML(data)
	return string(data)
}

func (s *Server) CheckImage(ctx context.Context, req *kateway.CheckImageRequest) (*kateway.CheckImageResponse, error) {
	var (
		regions []string
		err     error
	)
	if req.GetRegion() == "" {
		regions, err = services.Get().Kateway().ListCCRRegion()
		if err != nil {
			return nil, err
		}
	} else {
		regions = []string{req.Region}
	}

	src := config.Get().GetImageName(req.GetName())
	go func() {
		alarmReq := &alarm.Request{
			ObjType: "kateway",
			ObjName: src,
			ObjID:   req.Version,
			Content: fmt.Sprintf("启动镜像检查\n组件: %s\n版本：%s\n", req.GetName(), req.GetVersion()),
		}
		alarm.Send(alarmReq)

		err = image.Check(src, []string{req.Version}, regions)
		content := fmt.Sprintf("检查镜像 通过：\n源镜像：%s\n版本：%s\n", src, req.Version)
		if err != nil {
			content = fmt.Sprintf("检查镜像 失败：\n源镜像：%s\n版本：%s\n错误：%s\n", src, req.Version, err)
		}

		alarmReq = &alarm.Request{
			ObjType: "kateway",
			ObjName: src,
			ObjID:   req.Version,
			Content: content,
		}
		alarm.Send(alarmReq)
	}()

	return nil, nil
}

func allowUpdate(ctx context.Context, clusterID string, token string) (bool, error) {
	cluster, err := services.Get().Cluster().Get(ctx, clusterID)
	if err != nil {
		return false, fmt.Errorf("查询集群信息失败: %w", err)
	}

	// 禁止更新逻辑优先级最高
	if lo.Contains(config.Get().KOPS.DisallowUpdate.Clusters, clusterID) {
		return false, fmt.Errorf("该集群位于禁止更新列表中，禁止更新！")
	}

	if lo.Contains(config.Get().KOPS.DisallowUpdate.Appids, cluster.Appid) {
		return false, fmt.Errorf("该集群所属账号位于禁止更新列表中，禁止更新！")
	}

	// 如果显式指定了token，则允许更新，后续依赖ianvs来控制
	if token != "" {
		return true, nil
	}

	if lo.Contains(config.Get().KOPS.AllowUpdate.Clusters, clusterID) {
		return true, nil
	}

	if lo.Contains(config.Get().KOPS.AllowUpdate.Appids, cluster.Appid) {
		return true, nil
	}

	return false, fmt.Errorf("禁止不传入token更新用户集群！")
}

func (s *Server) SetImage(req *kateway.SetImageRequest, stream kateway.Kateway_SetImageServer) (err error) {
	ctx := stream.Context()

	zhiyanClient := zhiyan.New(map[string]any{
		"user":    req.GetTarget().GetUser(),
		"token":   req.GetTarget().GetToken(),
		"cluster": req.GetTarget().GetCluster(),
	})
	writer := func(format string, args ...any) {
		data := fmt.Sprintf(format, args...)
		zhiyanClient.Send(data)
		stream.Send(&kateway.SetImageResponse{
			Data: fmt.Sprintf("%s %s", time.Now().Format("2006-01-02 15:04:05"), data),
		})
	}
	defer func() {
		if err != nil {
			writer("操作失败: %s", err)
			err = nil
		}
		writer("若有疑问，请复制以下部分反馈给相关同学!\n%s", zhiyanClient.URL())
	}()
	writer("SetImage %s", req)

	allow, err := allowUpdate(ctx, req.GetTarget().GetCluster(), req.GetTarget().GetToken())
	if err != nil {
		return err
	}
	if !allow {
		return fmt.Errorf("不允许操作该集群")
	}

	ctrl, err := s.controller(ctx, req.GetTarget())
	if err != nil {
		return err
	}

	return ctrl.SetImage(ctx, req.GetImage(), controller.SetImageOptions{
		Writer: writer,
	})
}

type Writer func(format string, args ...any)

func (s *Server) Mock(req *kateway.MockRequest, stream kateway.Kateway_MockServer) (err error) {
	ctx := stream.Context()

	zhiyanClient := zhiyan.New(map[string]any{
		"user":    req.GetTarget().GetUser(),
		"token":   req.GetTarget().GetToken(),
		"cluster": req.GetTarget().GetCluster(),
	})
	writer := func(format string, args ...any) {
		data := fmt.Sprintf(format, args...)
		zhiyanClient.Send(data)
		stream.Send(&kateway.MockResponse{
			Data: fmt.Sprintf("%s %s", time.Now().Format("2006-01-02 15:04:05"), data),
		})
	}
	defer func() {
		if err != nil {
			writer("操作失败: %s", err)
			err = nil
		}
		writer("若有疑问，请复制以下部分反馈给相关同学!\n%s", zhiyanClient.URL())
	}()
	writer("Mock %s", req)

	allow, err := allowUpdate(ctx, req.GetTarget().GetCluster(), req.GetTarget().GetToken())
	if err != nil {
		return err
	}
	if !allow {
		return fmt.Errorf("不允许操作该集群")
	}

	ctrl, err := s.controller(ctx, req.GetTarget())
	if err != nil {
		return err
	}

	_, err = ctrl.Dryrun(ctx, req.GetImage(), controller.DryrunOptions{
		Writer: writer,
	})
	if err != nil {
		return err
	}

	return nil
}

func (s *Server) Update(req *kateway.UpdateRequest, stream kateway.Kateway_UpdateServer) (err error) {
	ctx := stream.Context()

	zhiyanClient := zhiyan.New(map[string]any{
		"user":    req.GetTarget().GetUser(),
		"token":   req.GetTarget().GetToken(),
		"cluster": req.GetTarget().GetCluster(),
	})
	writer := func(format string, args ...any) {
		data := fmt.Sprintf(format, args...)
		zhiyanClient.Send(data)
		stream.Send(&kateway.UpdateResponse{
			Data: fmt.Sprintf("%s %s", time.Now().Format("2006-01-02 15:04:05"), data),
		})
	}
	defer func() {
		if err != nil {
			writer("操作失败: %s", err)
			err = nil
		}
		writer("若有疑问，请复制以下部分反馈给相关同学!\n%s", zhiyanClient.URL())
	}()
	writer("Update %s", req)

	allow, err := allowUpdate(ctx, req.GetTarget().GetCluster(), req.GetTarget().GetToken())
	if err != nil {
		return err
	}
	if !allow {
		return fmt.Errorf("不允许操作该集群")
	}

	ctrl, err := s.controller(ctx, req.GetTarget())
	if err != nil {
		return err
	}

	_, err = ctrl.Update(ctx, req.GetImage(), controller.UpdateOptions{
		Force: req.GetForce(),
		DryrunOptions: controller.DryrunOptions{
			Writer: writer,
		},
	})

	return
}

func (s *Server) GetTask(ctx context.Context, req *kateway.GetTaskRequest) (*kateway.GetTaskResponse, error) {
	t, err := services.Get().Task().GetByID(ctx, req.Id)
	if err != nil {
		return nil, err
	}

	var (
		finishedAt string
	)
	if t.FinishedAt != nil {
		finishedAt = t.FinishedAt.Format(time.RFC3339)
	}

	output := fmt.Sprintf(`ID: %s
Type: %s
State: %s
ParentID: %s
SubType: %s
Input: %q
Progress: %s
Creator: %s
Status: %s
LastError: %q
CreatedAt: %s
FinishedAt: %s`,
		t.ID, t.Type, t.State, t.ParentID, t.SubType, t.Input, t.Progress, t.Creator, t.Status, t.LastError, t.CreatedAt, finishedAt)

	return &kateway.GetTaskResponse{
		Data: output,
	}, nil
}

func (s *Server) ListTask(ctx context.Context, req *kateway.ListTaskRequest) (*kateway.ListTaskResponse, error) {
	filters, err := parseFilters(req.Filters)
	if err != nil {
		return nil, err
	}

	opts := []task.ListOption{task.WithSelectColumns("ID", "ParentID", "Type", "State", "CreatedAt")}
	for _, f := range filters {
		switch f.name {
		case "ID":
			opts = append(opts, task.WithTaskIDs(f.values...))
		case "State":
			opts = append(opts, task.WithStates(lo.Map(f.values, func(v string, _ int) model.TaskState { return model.TaskState(v) })...))
		case "SubType":
			opts = append(opts, task.WithSubTypes(f.values...))
		case "ParentID":
			opts = append(opts, task.WithParentIDs(f.values...))
		case "Type":
			opts = append(opts, task.WithTypes(f.values...))
		default:
			return nil, fmt.Errorf("unknown filter: %s", f.name)
		}
	}
	if req.Limit != 0 {
		opts = append(opts, task.WithLimit(int(req.Limit)))
	}
	if req.Offset != 0 {
		opts = append(opts, task.WithOffset(int(req.Offset)))
	}

	svc := services.Get().Task()
	ts, total, err := svc.List(ctx, opts...)
	if err != nil {
		return nil, err
	}
	table := uitable.New()
	table.Separator = " "
	table.AddRow("ID", "Type", "State", "ParentID", "CreatedAt")
	for _, t := range ts {
		table.AddRow(t.ID, t.Type, t.State, t.ParentID, t.CreatedAt.Format(time.RFC3339))
	}
	output := fmt.Sprintf("Total: %d\n%s", total, table)
	return &kateway.ListTaskResponse{
		Data: output,
	}, nil
}

func (s *Server) UpdateArgs(req *kateway.ArgsRequest, stream kateway.Kateway_UpdateArgsServer) (err error) {
	ctx := stream.Context()

	zhiyanClient := zhiyan.New(map[string]any{
		"user":    req.GetTarget().GetUser(),
		"token":   req.GetTarget().GetToken(),
		"cluster": req.GetTarget().GetCluster(),
	})
	writer := func(format string, args ...any) {
		data := fmt.Sprintf(format, args...)
		zhiyanClient.Send(data)
		stream.Send(&kateway.ArgsResponse{
			Data: fmt.Sprintf("%s %s", time.Now().Format("2006-01-02 15:04:05"), data),
		})
	}
	defer func() {
		if err != nil {
			writer("操作失败: %s", err)
			err = nil
		}
		writer("若有疑问，请复制以下部分反馈给相关同学!\n%s", zhiyanClient.URL())
	}()
	writer("UpdateArgs %s", req)

	// 权限校验
	allow, err := allowUpdate(ctx, req.GetTarget().GetCluster(), req.GetTarget().GetToken())
	if err != nil {
		return err
	}
	if !allow {
		return fmt.Errorf("不允许操作该集群")
	}
	ctrl, err := s.controller(ctx, req.GetTarget())
	if err != nil {
		return err
	}

	// 遍历args，将格式进行转换
	var args []string
	for _, arg := range req.GetArgs() {
		args = append(args, fmt.Sprintf("--%s", strings.ReplaceAll(arg, ":", "=")))
	}
	return ctrl.UpdateArgs(ctx, req.GetOperation(), args, controller.UpdateOptions{
		Force: req.GetForce(),
		DryrunOptions: controller.DryrunOptions{
			Writer: writer,
		},
	})
}

func (s *Server) CreateTask(req *kateway.CreateTaskRequest, stream kateway.Kateway_CreateTaskServer) error {
	if !task.IsRegistered(req.Type) {
		return fmt.Errorf("unknown task type: %q", req.Type)
	}
	ctx := stream.Context()
	t := &model.Task{
		Type:    req.Type,
		Input:   req.Input,
		Creator: req.Creator,
	}
	svc := services.Get().Task()
	err := svc.CreateByTasks(ctx, t)
	if err != nil {
		return fmt.Errorf("failed to create task: %w", err)
	}
	if !req.Wait {
		return stream.Send(&kateway.CreateTaskResponse{
			Data: fmt.Sprintf("ID: %s", t.ID),
		})
	}

	taskEqual := func(a, b *model.Task) bool {
		return a.State == b.State && a.LastError == b.LastError &&
			reflect.DeepEqual(a.Progress, b.Progress) && reflect.DeepEqual(a.Status.Extension, b.Status.Extension)
	}

	toString := func(t *model.Task) string {
		return fmt.Sprintf(`--------- Task 状态更新 --------
ID: %s
State: %s
LastError: %s
Progress: %s
Status: %s
`, t.ID, t.State, t.LastError, t.Progress, t.Status)
	}

	return svc.WaitTask(ctx, t.ID, func(tt *model.Task) {
		if !taskEqual(t, tt) {
			t = tt
			_ = stream.Send(&kateway.CreateTaskResponse{
				Data: toString(tt),
			})
		}
	})
}
