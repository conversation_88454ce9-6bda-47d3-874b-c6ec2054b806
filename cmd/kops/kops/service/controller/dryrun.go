package controller

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/samber/lo"
	appsv1 "k8s.io/api/apps/v1"
	batchv1 "k8s.io/api/batch/v1"
	corev1 "k8s.io/api/core/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/wait"
	"k8s.io/client-go/kubernetes"
	"k8s.io/klog/v2"
	"sigs.k8s.io/yaml"

	"git.woa.com/kateway/kateway-server/pkg/dryrun"
)

const (
	dryrunRBACName = "service-controller-readonly"
)

type UpdateOptions struct {
	Force bool
	DryrunOptions
}

type Writer func(format string, args ...any)

type SetImageOptions struct {
	Writer Writer
}

type DryrunOptions struct {
	/*
		是否对集群内的 ingress 进行预检，仅对融合版本的 service-controller 生效
		当值为 nil 时，预检流程自动进行判断。若当前的 service-controller 为融合模式，则执行 ingress 预检
	*/
	DryrunIngress *bool
	/*
		是否关闭service预检，仅对融合版本的 service-controller 生效
	*/
	DisableDryrunService bool

	// 基于该 deployment 配置构建 dryrun job
	Deployment *appsv1.Deployment

	Writer func(format string, args ...any)

	// 是否保留 dryrun pod
	KeepDryrunPod bool

	Updaters []DeploymentUpdater
}

func (c Controller) Dryrun(ctx context.Context, version string, opts DryrunOptions) (res dryrun.Records, err error) {
	if opts.Writer == nil {
		opts.Writer = func(_ string, _ ...any) {}
	}
	ctx, cancel := context.WithTimeout(ctx, 10*time.Minute)
	defer cancel()

	defer func() {
		if err != nil {
			return
		}

		if len(res) > 0 {
			data, _ := yaml.Marshal(res)
			opts.Writer("预检失败:\n %s", data)
		} else {
			opts.Writer("预检成功")
		}
	}()

	opts.Writer("预检开始: 集群=%s 版本=%s", c.GetCluster().ClusterID, version)

	client := c.GetClientset().K8sCli
	ns := c.namespacedName.Namespace
	err = ensureDryrunRBAC(ctx, client, ns)
	if err != nil {
		return nil, err
	}

	args, err := c.genDryrunArgs(ctx, version, opts)
	if err != nil {
		return nil, err
	}

	job, err := c.genDryrunJob(ctx, version, opts.Deployment, opts.Updaters, args...)
	if err != nil {
		return nil, err
	}

	if !opts.KeepDryrunPod {
		defer func() {
			if err := cleanUpJob(ctx, client, job); err != nil {
				opts.Writer("job %s/%s 清理失败: %s", ns, job.Name, err)
			}
		}()
	}

	// 清理旧job，避免创建报错
	err = cleanUpJob(ctx, client, job)
	if err != nil {
		return nil, err
	}

	job, err = client.BatchV1().Jobs(ns).Create(ctx, job, metav1.CreateOptions{})
	if err != nil {
		return nil, err
	}
	opts.Writer("job %s/%s 创建成功", ns, job.Name)

	// 等待job完成
	jobErr := wait.PollUntilWithContext(ctx, 5*time.Second, func(ctx context.Context) (bool, error) {
		job, err := client.BatchV1().Jobs(ns).Get(ctx, job.Name, metav1.GetOptions{})
		if err != nil {
			return false, fmt.Errorf("查询job失败: %w", err)
		}
		if job.Status.CompletionTime == nil {
			job.Status.CompletionTime = &metav1.Time{Time: time.Now()}
		}

		if job.Status.Succeeded == 1 {
			opts.Writer("job %s/%s 执行成功 完成时间=%s 耗时=%s", ns, job.Name, job.Status.CompletionTime.Time.Format(time.RFC3339), job.Status.CompletionTime.Time.Sub(job.CreationTimestamp.Time).Round(time.Second))
			return true, nil
		}

		if job.Status.Failed > 0 {
			return false, fmt.Errorf("job %s/%s 执行失败 完成时间=%s 耗时=%s", ns, job.Name, job.Status.CompletionTime.Time.Format(time.RFC3339), job.Status.CompletionTime.Time.Sub(job.CreationTimestamp.Time).Round(time.Second))
		}

		if job.Status.Active > 0 {
			selector, _ := metav1.LabelSelectorAsSelector(job.Spec.Selector)
			pods, err := client.CoreV1().Pods(ns).List(ctx, metav1.ListOptions{
				LabelSelector: selector.String(),
			})
			if err != nil {
				return false, err
			}
			if len(pods.Items) == 0 {
				return false, fmt.Errorf("未找到有效job pod")
			}
			pod := pods.Items[0]
			opts.Writer("job %s/%s 执行中 Pod=%s 状态=%s 耗时=%s", ns, job.Name, pod.Name, pod.Status.Phase, time.Since(pod.CreationTimestamp.Time).Round(time.Second))
		}

		return false, nil
	})
	// 获取pod日志
	selector, _ := metav1.LabelSelectorAsSelector(job.Spec.Selector)
	pods, err := client.CoreV1().Pods(ns).List(ctx, metav1.ListOptions{
		LabelSelector: selector.String(),
	})
	if err != nil {
		return nil, err
	}
	if len(pods.Items) == 0 {
		return nil, fmt.Errorf("未找到有效job pod")
	}
	pod := pods.Items[0]
	opts.Writer("job %s/%s 执行结束 Pod=%s 状态=%s", ns, job.Name, pod.Name, pod.Status.Phase)
	if pod.Status.Phase == corev1.PodPending {
		return nil, fmt.Errorf("pod %s/%s 状态为Pending", ns, pod.Name)
	}
	logs, err := c.GetLogs(ctx, PodLogOptions{
		PodName: pods.Items[0].Name,
	})
	if err != nil {
		return nil, err
	}

	lines := logs[pods.Items[0].Name]
	// 如果job执行失败，则输出错误和pod原始日志
	if jobErr != nil {
		return nil, fmt.Errorf("%s:\n%s", jobErr, strings.Join(lines, "\n"))
	}

	rs := dryrun.ParseRecordsFromLogs(lines)
	return rs, nil
}

func (c Controller) genDryrunArgs(ctx context.Context, version string, opts DryrunOptions) ([]string, error) {
	var (
		args              = []string{}
		dryrunFlag        = "--mock-run=true"
		dryrunIngressFlag = "--dry-run-ingress=true"
	)

	if c.IsService() {
		mergeSupported := NewImage(version).CheckVersion(">=2.5.0")
		if !mergeSupported {
			if opts.DryrunIngress != nil || opts.DisableDryrunService {
				return nil, errors.New("can not set DryrunIngress or disable dryrun service for service controllers before version 2.5.0")
			}
		}

		var dryrunIngress bool
		if opts.DryrunIngress != nil {
			dryrunIngress = *opts.DryrunIngress
		} else {
			enabled, err := c.MergedIngressControllerEnabled(ctx)
			if err != nil {
				return nil, fmt.Errorf("failed to check if the merged ingress controller is enabled: %w", err)
			}
			dryrunIngress = enabled
		}

		if !dryrunIngress && opts.DisableDryrunService {
			return nil, errors.New("ingress dryrun and service dryrun are both disabled")
		}

		if !opts.DisableDryrunService {
			args = append(args, dryrunFlag)
		}
		if dryrunIngress {
			args = append(args, dryrunIngressFlag)
		}
	} else {
		if opts.DryrunIngress != nil || opts.DisableDryrunService {
			return nil, errors.New("can not set DryrunIngress or disable dryrun service for ingress controllers")
		}
		args = append(args, dryrunFlag)
	}
	return args, nil
}

func (c Controller) genDryrunJob(ctx context.Context, version string, base *appsv1.Deployment, inUpdaters []DeploymentUpdater, args ...string) (*batchv1.Job, error) {
	if base == nil {
		var err error
		base, err = c.GetDeployment(ctx, GetDeploymentOptions{})
		if err != nil {
			return nil, err
		}
	}
	// 深拷贝，避免修改原始对象
	deploy := base.DeepCopy()

	updaters := []DeploymentUpdater{
		c.BuildContainerImageUpdater(func(oldImage string) string {
			return c.getNewImage(oldImage, version)
		}),
		c.BuildContainerUpdater(func(c *corev1.Container) {
			c.ImagePullPolicy = corev1.PullAlways
			c.Lifecycle = &corev1.Lifecycle{
				PreStop: &corev1.LifecycleHandler{
					Exec: &corev1.ExecAction{
						Command: []string{"/bin/sh", "-c", "sleep 10s"}, // 防止 job pod 退出太快导致无法获取 pod 日志
					},
				},
			}
		}),
		c.BuildContainerArgsUpdater(func(s []string) []string {
			return append(s, args...)
		}),
		c.BuildTemplateUpdater(func(spec *corev1.PodTemplateSpec) {
			spec.Labels = nil                                   // 避免被正常svc选中
			spec.Spec.RestartPolicy = corev1.RestartPolicyNever // 无需重启
			spec.Spec.ServiceAccountName = dryrunRBACName       // 专用sa
			spec.Spec.TopologySpreadConstraints = nil           // mock pod 不需要指定拓扑限制，否则在小地域可能导致pod调度失败
			spec.Spec.Affinity = nil                            // 防止 autopilot-controller 添加的 affinity 规则导致 pod 无法调度
		}),
	}

	if len(inUpdaters) > 0 {
		updaters = append(updaters, inUpdaters...)
	}

	for _, u := range updaters {
		if err := u(deploy); err != nil {
			return nil, err
		}
	}

	return &batchv1.Job{
		TypeMeta: metav1.TypeMeta{Kind: "Job"},
		ObjectMeta: metav1.ObjectMeta{
			Name:      c.Name() + "-mock-job",
			Namespace: deploy.Namespace,
		},
		Spec: batchv1.JobSpec{
			BackoffLimit: lo.ToPtr(int32(0)),
			Template:     deploy.Spec.Template,
		},
	}, nil
}

func cleanUpJob(ctx context.Context, client kubernetes.Interface, job *batchv1.Job) error {
	_, err := client.BatchV1().Jobs(job.Namespace).Get(ctx, job.Name, metav1.GetOptions{})
	if err != nil {
		if !apierrors.IsNotFound(err) {
			return err
		}
		return nil
	}

	klog.Infof("job %s/%s 删除中", job.Namespace, job.Name)
	if err := client.BatchV1().Jobs(job.Namespace).Delete(ctx, job.Name, metav1.DeleteOptions{
		PropagationPolicy: lo.ToPtr(metav1.DeletePropagationForeground),
	}); err != nil {
		return err
	}

	// // 未知原因导致删除job时并没有回收pod，所以主动查询清理一次
	// pods, err := client.CoreV1().Pods(job.Namespace).List(ctx, metav1.ListOptions{
	// 	LabelSelector: fmt.Sprintf("job-name=%s", job.Name),
	// })
	// if err != nil {
	// 	return err
	// }
	// for _, pod := range pods.Items {
	// 	err := client.CoreV1().Pods(job.Namespace).Delete(ctx, pod.Name, metav1.DeleteOptions{})
	// 	if err != nil {
	// 		return err
	// 	}
	// }

	err = wait.PollUntilWithContext(ctx, 1*time.Second, func(ctx context.Context) (bool, error) {
		_, err := client.BatchV1().Jobs(job.Namespace).Get(ctx, job.Name, metav1.GetOptions{})
		// 等待job删除
		if !apierrors.IsNotFound(err) {
			return false, err
		}
		klog.Infof("job %s/%s 已删除", job.Namespace, job.Name)

		return true, nil
	})
	if err != nil {
		return err
	}

	return nil
}
