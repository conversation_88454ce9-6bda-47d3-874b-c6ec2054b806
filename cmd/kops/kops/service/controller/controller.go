package controller

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"net/http"
	"net/url"
	"reflect"
	"strings"
	"time"

	"github.com/Masterminds/semver"
	"github.com/gosuri/uitable"
	"github.com/patrickmn/go-cache"
	"github.com/samber/lo"
	appsv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/client-go/kubernetes/scheme"
	restclient "k8s.io/client-go/rest"
	"k8s.io/client-go/tools/leaderelection/resourcelock"
	"k8s.io/client-go/tools/remotecommand"
	"k8s.io/client-go/util/retry"
	"k8s.io/klog/v2"

	cluster2 "git.woa.com/kateway/kateway-server/cmd/kops/common/cluster"
	"git.woa.com/kateway/kateway-server/cmd/kops/kops/model"
	services "git.woa.com/kateway/kateway-server/cmd/kops/kops/service"
	"git.woa.com/kateway/kateway-server/pkg/service/cluster"
	"git.woa.com/kateway/kateway-server/pkg/tmp/telemetry/log"
	"git.woa.com/kateway/kateway-server/pkg/version"
)

var (
	ErrResourceNotFound      = errors.New("resource not found")
	ErrInvalidControllerKind = errors.New("invalid controller kind")
	ErrFeatureNotImplemented = errors.New("feature not implemented")
)

type LeaderStatus struct {
	Exists          bool
	Identity        string
	ExpiredAt       time.Time
	RenewedAt       time.Time
	ExpiredDuration time.Duration
}

type Kind string

const (
	KindService Kind = "service"
	KindIngress Kind = "ingress"
)

type Controller struct {
	kind           Kind
	ins            Instance
	namespacedName types.NamespacedName
	runningInMeta  bool

	cache *cache.Cache
}

func (c Controller) GetCluster() *model.Cluster {
	return c.ins.cluster
}

func (c Controller) RunningInEKS() bool {
	return c.ins.cluster.IsEKS()
}

func (c Controller) RunningInMeta() bool {
	return c.runningInMeta
}

func (c Controller) Kind() Kind {
	return c.kind
}

func (c Controller) IsService() bool {
	return c.kind == KindService
}

func (c Controller) IsIngress() bool {
	return c.kind == KindIngress
}

func (c Controller) Name() string {
	return lo.Ternary(c.kind == KindService, "service-controller", "ingress-controller")
}

func (c Controller) NamespacedName() types.NamespacedName {
	return c.namespacedName
}

func (c Controller) GetClusterClientset() *cluster.ClientsSet {
	return c.ins.GetClusterClientset()
}

func (c Controller) GetMetaClusterClientset() *cluster.ClientsSet {
	return c.ins.GetMetaClusterClientset()
}

func (c Controller) GetClientset() *cluster.ClientsSet {
	if c.runningInMeta {
		return c.ins.metaClusterClientSet
	}
	return c.ins.clusterClientSet
}

func (c Controller) Enabled(ctx context.Context) (bool, error) {
	d, err := c.GetDeployment(ctx, GetDeploymentOptions{})
	if err != nil {
		if !errors.Is(err, ErrResourceNotFound) {
			return false, err
		}
		return false, nil
	}
	return d.Status.ReadyReplicas > 0, nil
}

func (c Controller) GetRESTConfig() (*restclient.Config, error) {
	cls := c.ins.cluster
	if c.runningInMeta {
		cls = c.ins.metaCluster
	}
	cfg, err := cluster2.GetTKEClusterRestConfig(cls.Region, cls.Appid, cls.ClusterID)
	if err != nil {
		return nil, err
	}
	return cfg, nil
}

func (c Controller) GetImage(ctx context.Context) (*Image, error) {
	d, err := c.GetDeployment(ctx, GetDeploymentOptions{BypassCache: true})
	if err != nil {
		return nil, err
	}
	ct, err := c.findContainerInDeployment(d)
	if err != nil {
		return nil, err
	}
	return NewImage(ct.Image), nil
}

func (c Controller) CheckVersion(ctx context.Context, constraint string) bool {
	v, _, err := c.GetVersion(ctx)
	if err != nil {
		return false
	}

	ok, _ := version.Check(v.String(), constraint)
	return ok
}

func (c Controller) GetVersion(ctx context.Context) (*semver.Version, string, error) {
	image, err := c.GetImage(ctx)
	if err != nil {
		return nil, "", err
	}
	tag := image.Tag
	v, err := version.Parse(tag)
	if err != nil {
		return nil, "", err
	}
	return v, tag, nil
}

type LeaderPod struct {
	*corev1.Pod
	Container       *corev1.Container
	ContainerStatus *corev1.ContainerStatus
}

func (c Controller) GetLeaderPod(ctx context.Context) (*LeaderPod, error) {
	status, err := c.GetLeaderStatus(ctx)
	if err != nil {
		return nil, err
	}
	pods, err := c.ListPod(ctx, ListPodsOptions{BypassCache: true})
	if err != nil {
		return nil, err
	}
	p, exists := lo.Find(pods, func(p corev1.Pod) bool {
		return strings.HasPrefix(status.Identity, p.Name)
	})
	if !exists {
		return nil, fmt.Errorf("find leader pod for %s error: %w", c.Name(), ErrResourceNotFound)
	}

	containerStatus, _ := c.GetContainerStatus(&p)
	leader := &LeaderPod{
		Pod:             &p,
		Container:       c.GetContainer(&p.Spec),
		ContainerStatus: &containerStatus,
	}

	return leader, nil
}

func (c Controller) GetAdminStatusRaw(ctx context.Context) ([]byte, error) {
	version251 := semver.MustParse("2.5.1")

	v, _, err := c.GetVersion(ctx)
	if err != nil {
		return nil, err
	}

	if v.LessThan(version251) {
		return nil, ErrFeatureNotImplemented
	}

	return c.ExecLeaderAdmin(ctx, http.MethodGet, "/status", url.Values{})
}

func (c Controller) GetLeaderStatus(ctx context.Context) (*LeaderStatus, error) {
	var record *resourcelock.LeaderElectionRecord
	if c.CheckVersion(ctx, leaseVersionConstraints) {
		lease, err := c.GetClusterClientset().K8sCli.CoordinationV1().Leases(metav1.NamespaceSystem).Get(ctx, c.Name(), metav1.GetOptions{})
		if err != nil {
			return nil, err
		}
		record = &resourcelock.LeaderElectionRecord{
			HolderIdentity:       *lease.Spec.HolderIdentity,
			LeaseDurationSeconds: int(*lease.Spec.LeaseDurationSeconds),
			AcquireTime:          metav1.NewTime(lease.Spec.AcquireTime.Time),
			RenewTime:            metav1.NewTime(lease.Spec.RenewTime.Time),
			LeaderTransitions:    int(*lease.Spec.LeaseTransitions),
		}
	} else {
		name, err := c.getLeaderElectionResourceName(ctx)
		if err != nil {
			return nil, err
		}

		record, err = services.Get().Cluster().GetLeaderRecord(ctx, c.GetClusterClientset().K8sCli, metav1.NamespaceSystem, name)
		if err != nil {
			return nil, err
		}
	}

	now := time.Now()
	expiredAt := record.RenewTime.Add(time.Duration(record.LeaseDurationSeconds) * time.Second)
	diff := now.Sub(expiredAt).Round(time.Second)
	return &LeaderStatus{
		Exists:          diff <= 0,
		Identity:        record.HolderIdentity,
		ExpiredAt:       expiredAt,
		RenewedAt:       record.RenewTime.Time,
		ExpiredDuration: lo.Ternary(diff > 0, diff, 0),
	}, nil
}

type PodLogOptions struct {
	corev1.PodLogOptions
	PodName string
}

func (c Controller) GetLogs(ctx context.Context, opts PodLogOptions) (map[string][]string, error) {
	var targets []types.NamespacedName
	if opts.PodName != "" {
		targets = append(targets, types.NamespacedName{
			Namespace: c.namespacedName.Namespace,
			Name:      opts.PodName,
		})
	} else {
		pods, err := c.ListRunningPod(ctx, ListPodsOptions{BypassCache: true})
		if err != nil {
			return nil, err
		}
		targets = lo.Map(pods, func(p corev1.Pod, _ int) types.NamespacedName {
			return types.NamespacedName{
				Namespace: p.Namespace,
				Name:      p.Name,
			}
		})
	}

	cli := c.GetClientset().K8sCli
	opts.Container = c.getContainerName()
	logsByPod := map[string][]string{}
	for _, t := range targets {
		var b bytes.Buffer
		req := cli.CoreV1().Pods(t.Namespace).GetLogs(t.Name, &opts.PodLogOptions)
		rc, err := req.Stream(ctx)
		if err != nil {
			return nil, err
		}
		defer rc.Close()
		_, _ = b.ReadFrom(rc)
		logsByPod[t.Name] = strings.Split(b.String(), "\n")
	}
	return logsByPod, nil
}

func (c Controller) ListRunningPod(ctx context.Context, opts ListPodsOptions) ([]corev1.Pod, error) {
	opts.Filters = append(opts.Filters, func(p corev1.Pod) bool {
		return p.Status.Phase == corev1.PodRunning
	})
	return c.ListPod(ctx, opts)
}

type ListPodsOptions struct {
	BypassCache bool
	Filters     []func(corev1.Pod) bool
}

func (c Controller) ListPod(ctx context.Context, opts ListPodsOptions) ([]corev1.Pod, error) {
	pods, err := c.listPod(ctx, opts.BypassCache)
	if err != nil {
		return nil, err
	}
	for _, f := range opts.Filters {
		pods = lo.Filter(pods, func(p corev1.Pod, _ int) bool {
			return f(p)
		})
	}
	return pods, nil
}

func (c Controller) ListPodToString(ctx context.Context, opts ListPodsOptions) (string, error) {
	leaderPod, _ := c.GetLeaderPod(ctx) // 忽略查找leader pod失败，只是为了展示leader标记
	pods, err := c.ListPod(ctx, opts)
	if err != nil {
		return "", err
	}
	_, expectVersion, err := c.GetVersion(ctx)
	if err != nil {
		return "", err
	}

	table := uitable.New()
	table.Separator = " "
	table.AddRow("Pod", "版本", "状态", "重启", "创建时间")
	for _, pod := range pods {
		// 如果存在leader，则展示leader标记
		if leaderPod != nil {
			if pod.Name == leaderPod.Name {
				pod.Name += " *"
			}
		}
		container := pod.Spec.Containers[0]
		containerStatus, ok := lo.Find(pod.Status.ContainerStatuses, func(item corev1.ContainerStatus) bool {
			return item.Name == container.Name
		})
		if !ok {
			continue
		}
		podVersion := strings.Split(container.Image, ":")[1]
		if podVersion != expectVersion {
			podVersion += "(老版本)"
		}

		table.AddRow(pod.Name, podVersion, pod.Status.Phase, containerStatus.RestartCount, pod.CreationTimestamp)
	}

	return table.String(), nil
}

func (c Controller) GetContainer(spec *corev1.PodSpec) *corev1.Container {
	_, index, ok := lo.FindIndexOf(spec.Containers, func(item corev1.Container) bool {
		return item.Name == c.getContainerName()
	})
	if !ok {
		return nil
	}

	return &spec.Containers[index]
}

func (c Controller) GetContainerStatus(pod *corev1.Pod) (corev1.ContainerStatus, bool) {
	return lo.Find(pod.Status.ContainerStatuses, func(item corev1.ContainerStatus) bool {
		return item.Name == c.getContainerName()
	})
}

type GetDeploymentOptions struct {
	BypassCache bool
}

func (c Controller) GetDeployment(ctx context.Context, opts GetDeploymentOptions) (*appsv1.Deployment, error) {
	if obj, exists := c.cache.Get(c.namespacedName.String()); exists && !opts.BypassCache {
		return obj.(*appsv1.Deployment), nil
	}

	cli := c.GetClientset().K8sCli
	deploy, err := cli.AppsV1().Deployments(c.namespacedName.Namespace).Get(ctx, c.namespacedName.Name, metav1.GetOptions{})
	if err != nil {
		if apierrors.IsNotFound(err) {
			return nil, fmt.Errorf("deployment %s not found: %w", c.namespacedName, ErrResourceNotFound)
		}
		return nil, err
	}

	c.cache.SetDefault(c.namespacedName.String(), deploy)
	return deploy, nil
}

type DeploymentUpdater func(*appsv1.Deployment) error

func (c Controller) BuildTemplateUpdater(fn func(*corev1.PodTemplateSpec)) DeploymentUpdater {
	return func(d *appsv1.Deployment) error {
		fn(&d.Spec.Template)
		return nil
	}
}

func (c Controller) BuildContainerUpdater(fn func(*corev1.Container)) DeploymentUpdater {
	return func(d *appsv1.Deployment) error {
		container, err := c.findContainerInDeployment(d)
		if err != nil {
			return err
		}
		fn(container)
		return nil
	}
}

func (c Controller) BuildContainerImageUpdater(fn func(string) string) DeploymentUpdater {
	return c.BuildContainerUpdater(func(ct *corev1.Container) {
		ct.Image = fn(ct.Image)
	})
}

func (c Controller) BuildContainerArgsUpdater(fn func([]string) []string) DeploymentUpdater {
	return c.BuildContainerUpdater(func(ct *corev1.Container) {
		var (
			set     func(args []string)
			oldArgs []string
		)

		if c.IsIngress() {
			oldArgs = ct.Args
			set = func(args []string) {
				ct.Args = args
			}
		} else if len(ct.Command) > 0 && !c.ins.cluster.IsIndependent() {
			oldArgs = ct.Command
			set = func(args []string) {
				ct.Command = args
			}
		} else {
			l := len(ct.Args)
			last := ct.Args[l-1]
			oldArgs = strings.Split(last, " ")

			set = func(args []string) {
				ct.Args[l-1] = strings.Join(args, " ")
			}
		}

		newArgs := fn(oldArgs)
		set(newArgs)
	})
}

func (c Controller) GetCurArgs(ct *corev1.Container) []string {
	// ingress
	if c.IsIngress() {
		return ct.Args
	}
	// 独立集群 service
	if len(ct.Command) > 0 && !c.ins.cluster.IsIndependent() {
		return ct.Command
	}
	// 托管集群 service
	l := len(ct.Args)
	last := ct.Args[l-1]
	return strings.Split(last, " ")
}

func (c Controller) checkUpdateDeployment(deployment *appsv1.Deployment) error {
	container, err := c.findContainerInDeployment(deployment)
	if err != nil {
		return err
	}

	if lo.Contains(container.Command, "--enable-clusterip=true") {
		return fmt.Errorf("禁止更新开启了 --enable-clusterip=true 的老版本 eks 1.0")
	}

	if NewImage(container.Image).CheckVersion("<v2.0.0") {
		return fmt.Errorf("当前集群版本过低，无法升级: %s", container.Image)
	}

	return nil
}

func (c Controller) UpdateDeployment(ctx context.Context, updaters ...DeploymentUpdater) error {
	return retry.RetryOnConflict(retry.DefaultBackoff, func() error {
		oldDeployment, err := c.GetDeployment(ctx, GetDeploymentOptions{BypassCache: true})
		if err != nil {
			return err
		}

		if err = c.checkUpdateDeployment(oldDeployment); err != nil {
			return err
		}

		newDeployment := oldDeployment.DeepCopy()
		for _, fn := range updaters {
			if err := fn(newDeployment); err != nil {
				return err
			}
		}

		if reflect.DeepEqual(oldDeployment, newDeployment) {
			klog.InfoS("deployment not changed", "deployment", newDeployment)
			return nil
		}

		cli := c.GetClientset().K8sCli
		_, err = cli.AppsV1().Deployments(oldDeployment.Namespace).Update(ctx, newDeployment, metav1.UpdateOptions{})
		klog.InfoS("update deployment", "deployment", newDeployment, "error", err)
		return err
	})
}

func (c Controller) ExecLeaderAdmin(ctx context.Context, method, path string, query url.Values) ([]byte, error) {
	if c.kind != KindService {
		return nil, errors.New("only service-controller support exec leader admin")
	}

	status, err := c.GetLeaderStatus(ctx)
	if err != nil {
		return nil, err
	}
	if !status.Exists {
		return nil, errors.New("leader not exist")
	}

	restConfig, err := c.GetRESTConfig()
	if err != nil {
		return nil, err
	}

	restConfig.GroupVersion = &schema.GroupVersion{
		Version: "v1",
	}
	restConfig.APIPath = "/api"
	restConfig.NegotiatedSerializer = scheme.Codecs.WithoutConversion()
	if err = restclient.SetKubernetesDefaults(restConfig); err != nil {
		return nil, err
	}
	restClient, err := restclient.RESTClientFor(restConfig)
	if err != nil {
		return nil, err
	}
	execReq := restClient.Post().Resource("pods").
		Name(status.Identity).Namespace(c.namespacedName.Namespace).SubResource("exec")
	execReq.VersionedParams(&corev1.PodExecOptions{
		Container: "service-controller",
		Command:   []string{"/usr/bin/curl", "http://127.0.0.1" + path},
		Stdout:    true,
		Stderr:    true,
	}, scheme.ParameterCodec)

	exec, err := remotecommand.NewSPDYExecutor(restConfig, http.MethodPost, execReq.URL())
	if err != nil {
		return nil, err
	}

	var (
		outWriter = &bytes.Buffer{}
		errWriter = &bytes.Buffer{}
	)
	if err = exec.Stream(remotecommand.StreamOptions{
		Stdout: outWriter,
		Stderr: errWriter,
		// IdleTimeout: opts.IdleTimeout,
	}); err != nil {
		return nil, fmt.Errorf("exec request failed: %v: %v", err, errWriter.String())
	}
	log.FromContext(ctx).Info("exec request", "url", execReq.URL(), "stdout", outWriter.String(), "stderr", errWriter.String())

	return outWriter.Bytes(), nil
}

func (c Controller) MergedIngressControllerEnabled(ctx context.Context) (bool, error) {
	if c.IsIngress() {
		panic("controller kind must be service")
	}

	enabled, err := c.ins.Ingress().Enabled(ctx)
	if err != nil {
		return false, err
	}

	data, err := c.GetConfigData(ctx)
	if err != nil {
		return false, err
	}
	return !enabled && data["EnableIngressController"] == "true", nil
}

func (c Controller) GetConfigData(ctx context.Context) (map[string]string, error) {
	cm, err := c.GetConfig(ctx)
	if err != nil {
		return nil, err
	}
	return cm.Data, nil
}

func (c Controller) GetConfig(ctx context.Context) (*corev1.ConfigMap, error) {
	cli := c.GetClusterClientset().K8sCli
	cm, err := cli.CoreV1().ConfigMaps(metav1.NamespaceSystem).Get(ctx, c.getConfigMapName(), metav1.GetOptions{})
	if err != nil {
		if apierrors.IsNotFound(err) {
			return nil, ErrResourceNotFound
		}
		return nil, err
	}
	return cm, nil
}

func (c Controller) ProxyLeader(ctx context.Context, method, path string, query url.Values) ([]byte, error) {
	deploymentClientset := c.GetClientset()

	status, err := c.GetLeaderStatus(ctx)
	if err != nil {
		return nil, err
	}
	if !status.Exists {
		return nil, errors.New("leader not exist")
	}

	proxyRequest := deploymentClientset.K8sCli.CoreV1().RESTClient().Verb(method).
		Namespace(c.namespacedName.Namespace).
		Resource("pods").
		Name(status.Identity).
		SubResource("proxy").
		Suffix(path)

	for key, values := range query {
		for _, value := range values {
			proxyRequest.Param(key, value)
		}
	}
	log.FromContext(ctx).Info("proxy request", "url", proxyRequest.URL().String())

	return proxyRequest.DoRaw(ctx)
}

func (c Controller) findContainerInDeployment(d *appsv1.Deployment) (*corev1.Container, error) {
	_, index, exists := lo.FindIndexOf(d.Spec.Template.Spec.Containers, func(ct corev1.Container) bool {
		return ct.Name == c.getContainerName()
	})
	if !exists {
		return nil, fmt.Errorf("container %q not found: %w", c.getContainerName(), ErrResourceNotFound)
	}
	return &d.Spec.Template.Spec.Containers[index], nil
}

func (c Controller) getConfigMapName() string {
	switch c.kind {
	case KindService:
		return "tke-service-controller-config"
	case KindIngress:
		return "tke-ingress-controller-config"
	default:
		panic(ErrInvalidControllerKind)
	}
}

func (c Controller) getContainerName() string {
	switch c.kind {
	case KindService:
		return "service-controller"
	case KindIngress:
		return lo.Ternary(c.ins.cluster.IsEKS(), "ingress-controller", "l7-lb-controller")
	default:
		panic(ErrInvalidControllerKind)
	}
}

func (c Controller) listPod(ctx context.Context, bypassCache bool) ([]corev1.Pod, error) {
	const key = "pods"

	if v, exists := c.cache.Get(key); exists && !bypassCache {
		return v.([]corev1.Pod), nil
	}

	d, err := c.GetDeployment(ctx, GetDeploymentOptions{})
	if err != nil {
		return nil, err
	}
	if len(d.Spec.Selector.MatchLabels) == 0 {
		return nil, errors.New("empty selector")
	}
	list, err := c.GetClientset().K8sCli.CoreV1().Pods(d.Namespace).List(ctx, metav1.ListOptions{
		LabelSelector: labels.FormatLabels(d.Spec.Selector.MatchLabels),
	})
	if err != nil {
		return nil, err
	}

	c.cache.SetDefault(key, list.Items)

	return list.Items, nil
}

func (c Controller) getLeaderElectionResourceName(ctx context.Context) (name string, err error) {
	if c.CheckVersion(ctx, leaseVersionConstraints) {
		return c.Name(), nil
	}

	switch c.kind {
	case KindIngress:
		name = "qcloud-ingress-controller"
	case KindService:
		var v *semver.Version
		v, _, err = c.GetVersion(ctx)
		if err != nil {
			return
		}
		if c.ins.cluster.IsEKS() && v.Compare(semver.MustParse("2.3.0")) <= 0 {
			name = "tke-eks-vpcgw-controller-group-leader"
		} else {
			name = "qcloud-service-controller"
		}
	default:
		err = ErrInvalidControllerKind
	}
	return
}

func (c Controller) ConfigMapName() string {
	switch c.kind {
	case KindIngress:
		return "tke-ingress-controller-config"
	case KindService:
		return "tke-service-controller-config"
	default:
		panic(ErrInvalidControllerKind)
	}
}

func (c Controller) GetConfigMap(ctx context.Context) (*corev1.ConfigMap, error) {
	return c.GetClientset().K8sCli.CoreV1().ConfigMaps("kube-system").Get(ctx, c.ConfigMapName(), metav1.GetOptions{})
}

// ingress-controller在2.4.2版本引入自定义端口，升级2.4.2需要service-controller也同步升级到2.4.2或以上版本，
// 这里检查service-controller版本是否就绪。https://tapd.woa.com/tapd_fe/70108010/story/detail/1070108010121173269
func CheckIngressUpgrade(ctx context.Context, targetVersion string, svcController *Controller) error {
	tv := version.MustParse(targetVersion)
	version241 := version.MustParse("2.4.1")
	version242 := version.MustParse("2.4.2")

	if tv.LessThan(version241) {
		return nil
	}
	svcVersion, _, err := svcController.GetVersion(ctx)
	if err != nil {
		return err
	}
	if svcVersion.GreaterThan(version242) {
		return nil
	}
	if svcVersion.LessThan(version242) {
		return errors.New("将service-controller版本升级到v2.4.2及以上")
	}
	leader, err := svcController.GetLeaderPod(ctx)
	if err != nil {
		return err
	}
	statuses := leader.Status.ContainerStatuses
	status, exists := lo.Find(statuses, func(s corev1.ContainerStatus) bool {
		return s.Name == svcController.getContainerName()
	})
	if !exists {
		return fmt.Errorf("container status not found: %w", ErrResourceNotFound)
	}
	// 这里 49ba2fce96724d43d5bcf5e55ca8256dd 的hash值是最新的service-controller 2.4.2版本hash值末尾
	if !strings.HasSuffix(status.ImageID, "49ba2fce96724d43d5bcf5e55ca8256dd") {
		return errors.New("将service-controller版本升级到v2.5.0及以上")
	}
	return nil
}
