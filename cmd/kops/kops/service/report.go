package services

import (
	"context"
	"fmt"
	"sort"
	"strings"
	"sync"
	"time"

	"github.com/hashicorp/go-version"
	"github.com/patrickmn/go-cache"
	clb "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/clb/v20180317"
	"gopkg.in/yaml.v3"
	"gorm.io/gorm"

	"git.woa.com/kateway/pkg/telemetry/log"
	lbrv1alpha1 "git.woa.com/misakazhou/loadbalancer-resource-api/pkg/apis/loadbalancerresource/v1alpha1"

	"git.woa.com/kateway/kateway-server/cmd/kateway-server/app/config"
	"git.woa.com/kateway/kateway-server/cmd/kops/common"
	"git.woa.com/kateway/kateway-server/cmd/kops/kops/model"
	"git.woa.com/kateway/kateway-server/pkg/tmp/types/sets"
	"git.woa.com/kateway/kateway-server/pkg/tmp/web/cloudctx"
	region "git.woa.com/kateway/kateway-server/pkg/util/region"
)

// Inspection defines the interface for health check operations
type Inspection interface {
	// ListCluster list cluster info with appid
	ListCluster(ctx context.Context, appid string) ([]model.Cluster, error)

	// GetCluster retrieves a base info for a given cluster
	GetCluster(ctx context.Context, cluster *model.Cluster, refreshCache bool) (*model.ClusterInfo, error)

	// GetClusterReport retrieves a health report for a given cluster
	GetClusterReport(ctx context.Context, cluster *model.Cluster, refreshCache bool) (*model.ClusterReport, error)

	// GetClusterRiskReport retrieves a health report for a given cluster
	GetClusterRiskReport(ctx context.Context, cluster *model.Cluster, refreshCache bool) (*model.ClusterReport, error)

	// GetCLBReport retrieves a health report for a specific CLB within a cluster
	GetCLBReport(ctx context.Context, cluster *model.Cluster, clbID string, refreshCache, includeBackend bool) (*model.CLBReport, error)

	// GetResourceReport retrieves a health report for a specific resource within a cluster
	GetResourceReport(ctx context.Context, cluster *model.Cluster, resourceType, resourceNamespace, resourceName string, refreshCache bool) (*model.ResourceReport, error)
}

// InspectionService implements the Inspection interface
type InspectionService struct {
	db         *gorm.DB
	cacheStore *cache.Cache
}

// newInspectionService creates a new instance of InspectionService
func newInspectionService(db *gorm.DB) Inspection {
	return &InspectionService{
		db:         db,
		cacheStore: cache.New(5*time.Minute, 10*time.Minute),
	}
}

// List 集群健康检查报告
func (s *InspectionService) ListCluster(ctx context.Context, appid string) ([]model.Cluster, error) {
	clusterlist, err := Get().Cluster().List(ctx, WithAppID(appid))
	if err != nil {
		return nil, err
	}
	go func() {
		for index, cls := range clusterlist {
			s.GetCluster(ctx, &cls, false)
			log.Info("list cluster info", "clusterid", cls.ClusterID, "index", index, "total", len(clusterlist), "percentage", fmt.Sprintf("%.2f%%", 100*(float64(index)/float64(len(clusterlist)))))
		}
	}()

	return clusterlist, nil
}

// 获取集群健康检查报告
func (s *InspectionService) GetCluster(ctx context.Context, cluster *model.Cluster, refreshCache bool) (report *model.ClusterInfo, err error) {
	if !refreshCache {
		if item, _, ok := s.cacheStore.GetWithExpiration(cluster.ClusterID + "/" + "info"); ok {
			log.Info("get item from cache", "item", cluster.ClusterID+"/"+"info")
			report := item.(*model.ClusterInfo)
			return report, nil
		} else {
			log.Info("cache expired or not found", "item", cluster.ClusterID+"/"+"info")
		}
	}

	report, err = s.GetClusterInfo(ctx, cluster)
	if err != nil {
		return nil, err
	}
	stats, _ := s.GetClusterBaseStats(ctx, cluster)
	report.ClusterBaseStats = *stats

	if err := Get().LegacyTask().CreateOrUpdateClusterInfo(ctx, *report); err != nil {
		log.Error(err, "record cluster info failed")
	}

	s.cacheStore.Set(cluster.ClusterID+"/"+"info", report, 20*time.Minute)
	log.Info("update cache", "item", cluster.ClusterID+"/"+"info")
	return report, nil
}

// 获取集群健康检查报告
func (s *InspectionService) GetClusterReport(ctx context.Context, cluster *model.Cluster, refreshCache bool) (report *model.ClusterReport, err error) {
	if !refreshCache {
		if item, expireAt, ok := s.cacheStore.GetWithExpiration(cluster.ClusterID + "/" + "base"); ok {
			log.Info("get item from cache", "item", cluster.ClusterID+"/"+"base")
			report := item.(*model.ClusterReport)
			report.ExpireAt = expireAt.Format("2006-01-02 15:04:05")
			return report, nil
		} else {
			log.Info("cache expired or not found", "item", cluster.ClusterID+"/"+"base")
		}
	}

	clusterinfo, err := s.GetClusterInfo(ctx, cluster)
	if err != nil {
		return nil, err
	}
	// if err := Get().LegacyTask().CreateOrUpdateClusterInfo(ctx, *clusterinfo); err != nil {
	// 	log.Error(err, "record info failed")
	// }

	report, err = s.GetClusterCLB(ctx, cluster)
	if err != nil {
		return nil, err
	}
	report.ClusterInfo = *clusterinfo

	s.cacheStore.SetDefault(cluster.ClusterID+"/"+"base", report)
	log.Info("update cache", "item", cluster.ClusterID+"/"+"base")
	report.ExpireAt = time.Now().Add(5 * time.Minute).Format("2006-01-02 15:04:05")
	return report, nil
}

// 获取集群健康检查报告
func (s *InspectionService) GetClusterRiskReport(ctx context.Context, cluster *model.Cluster, refreshCache bool) (report *model.ClusterReport, err error) {
	if !refreshCache {
		if item, expireAt, ok := s.cacheStore.GetWithExpiration(cluster.ClusterID + "/" + "risk"); ok {
			log.Info("get item from cache", "item", cluster.ClusterID+"/"+"risk")
			report := item.(*model.ClusterReport)
			report.ExpireAt = expireAt.Format("2006-01-02 15:04:05")
			return report, nil
		} else {
			log.Info("cache expired or not found", "item", cluster.ClusterID+"/"+"risk")
		}
	}
	clusterinfo, err := s.GetClusterInfo(ctx, cluster)
	if err != nil {
		return nil, err
	}
	// if err := Get().LegacyTask().CreateOrUpdateClusterInfo(ctx, *clusterinfo); err != nil {
	// 	log.Error(err, "record info failed")
	// }

	report, err = s.GetClusterRiskCLB(ctx, cluster)
	if err != nil {
		return nil, err
	}
	report.ClusterInfo = *clusterinfo

	s.cacheStore.SetDefault(cluster.ClusterID+"/"+"risk", report)
	log.Info("update cache", "item", cluster.ClusterID+"/"+"risk")
	report.ExpireAt = time.Now().Add(5 * time.Minute).Format("2006-01-02 15:04:05")
	return report, nil
}

// 获取集群健康检查报告
func (s *InspectionService) GetClusterInfo(ctx context.Context, cluster *model.Cluster) (*model.ClusterInfo, error) {
	// 获取集群所属的UIN
	uin, err := common.GetUinByAppid(cluster.Appid)
	if err != nil {
		return nil, err
	}
	// 将UIN添加到上下文中
	ctx = cloudctx.WithUin(ctx, uin)

	return &model.ClusterInfo{
		ClusterName:   cluster.Name,
		ClusterRegion: region.MustGet(cluster.Region).Name,
		AppID:         int64(cluster.Appid),
		ClusterID:     cluster.ClusterID,
		ClusterType:   cluster.Type,
		Description:   cluster.Description,
		State:         cluster.State,
		MetaClusterID: cluster.MetaClusterID,
		NetworkInfo: model.NetworkInfo{
			ServiceCIDR:   cluster.ServiceCIDR,
			NetworkType:   cluster.NetworkType,
			KubeProxyMode: cluster.KubeProxyMode,
			K8SVersion:    cluster.K8SVersion,
			VpcID:         cluster.VpcID,
			SubnetID:      cluster.SubnetID,
		},

		ComponentInfo: Get().Cluster().GetComponentInfo(ctx, cluster),
		References: model.ClusterReferences{
			Quota:                    BuildDFSQuota(cluster.Appid, region.MustGet(cluster.Region).ID),
			OSSCluster:               BuildTKEOSSCluster(cluster.Appid, cluster.RegionID, cluster.ClusterID, uin),
			OSSUser:                  BuildTKEOSSUser(cluster.Appid, uin),
			MonitorMetaComponent:     GetMetaComponentMonitorURL(cluster),
			MonitorServiceController: GetServiceControllerMonitorURL(cluster),
		},
	}, nil
}

func (s *InspectionService) GetClusterBaseStats(ctx context.Context, cluster *model.Cluster) (*model.ClusterBaseStats, error) {
	stats := &model.ClusterBaseStats{}
	lbrlist, err := Get().Cluster().ListLBR(ctx, cluster)
	if err != nil {
		log.FromContext(ctx).Error(err, "inspection: list lbr error", "cluster", cluster.ClusterID)
	}
	svclist, err := Get().Cluster().ListService(ctx, cluster)
	if err != nil {
		log.FromContext(ctx).Error(err, "inspection: list svc error", "cluster", cluster.ClusterID)
	}
	inglist, err := Get().Cluster().ListIngress(ctx, cluster)
	if err != nil {
		log.FromContext(ctx).Error(err, "inspection: list ing error", "cluster", cluster.ClusterID)
	}
	tsclist, err := Get().Cluster().ListTSC(ctx, cluster)
	if err != nil {
		log.FromContext(ctx).Error(err, "inspection: list tsc error", "cluster", cluster.ClusterID)
	}

	stats.TotalCLBCount = len(lbrlist)
	stats.TotalServiceCount = len(svclist)
	for _, svc := range svclist {
		if value, ok := svc.Annotations["service.cloud.tencent.com/pass-to-target"]; ok {
			if value == "false" {
				log.FromContext(ctx).Info("risk service", "clusterID", cluster.ClusterID, "namespace", svc.Namespace, "name", svc.Name, "lbid", svc.Annotations["service.kubernetes.io/loadbalance-id"])
			}
		}
	}
	for _, ing := range inglist {
		if value, ok := ing.Annotations["ingress.cloud.tencent.com/pass-to-target"]; ok {
			if value == "false" {
				log.FromContext(ctx).Info("risk ingress", "clusterID", cluster.ClusterID, "namespace", ing.Namespace, "name", ing.Name, "lbid", ing.Annotations["kubernetes.io/ingress.qcloud-loadbalance-id"])
			}
		}
	}
	stats.TotalIngressCount = len(inglist)
	stats.TotalTKEServiceConfigCount = len(tsclist)

	return stats, nil
}

// 获取集群健康检查报告
func (s *InspectionService) GetClusterRiskCLB(ctx context.Context, cluster *model.Cluster) (*model.ClusterReport, error) {
	// 获取集群所属的UIN
	uin, err := common.GetUinByAppid(cluster.Appid)
	if err != nil {
		return nil, err
	}
	// 将UIN添加到上下文中
	ctx = cloudctx.WithUin(ctx, uin)
	// 初始化集群报告结构体，填充基本信息
	report := &model.ClusterReport{
		Template: "report_cluster_scan",
	}

	// 根据 LBR，检索集群内的 CLB 列表
	riskClbList, err := ScanRiskCLBList(ctx, cluster)
	if err != nil {
		return nil, err
	}

	// 基于扫描的CLB生成统计数据和分析数据
	report.Statistics = GenerateStats(riskClbList)
	if err := Get().LegacyTask().CreateOrUpdateClusterStats(ctx, cluster.ClusterID, report.Statistics); err != nil {
		log.Error(err, "record stats failed")
	}

	report.Analysises = GenerateAnalysises(riskClbList, false)

	return report, nil
}

// 获取集群健康检查报告
func (s *InspectionService) GetClusterCLB(ctx context.Context, cluster *model.Cluster) (*model.ClusterReport, error) {
	// 获取集群所属的UIN
	uin, err := common.GetUinByAppid(cluster.Appid)
	if err != nil {
		return nil, err
	}
	// 将UIN添加到上下文中
	ctx = cloudctx.WithUin(ctx, uin)
	// 初始化集群报告结构体，填充基本信息
	report := &model.ClusterReport{
		Template: "report_cluster",
	}

	// 根据 LBR，检索集群内的 CLB 列表
	clbList, err := ScanCLBList(ctx, cluster)
	if err != nil {
		return nil, err
	}

	// 基于扫描的CLB生成统计数据和分析数据
	report.Statistics = GenerateStats(clbList)
	report.Analysises = GenerateAnalysises(clbList, true)

	return report, nil
}

// 获取CLB健康检查报告
func (s *InspectionService) GetCLBReport(ctx context.Context, cluster *model.Cluster, lbid string, refreshCache, includeBackend bool) (*model.CLBReport, error) {
	if !refreshCache {
		if item, expireAt, ok := s.cacheStore.GetWithExpiration(cluster.ClusterID + "/" + lbid); ok {
			log.Info("get item from cache", "item", cluster.ClusterID+"/"+lbid)
			report := item.(*model.CLBReport)
			report.ExpireAt = expireAt.Format("2006-01-02 15:04:05")
			return report, nil
		} else {
			log.Info("cache expired or not found", "item", cluster.ClusterID+"/"+lbid)
		}
	}

	report := &model.CLBReport{Template: "report_clb"}

	// 获取用户标识并设置上下文
	uin, err := common.GetUinByAppid(uint64(cluster.Appid))
	if err != nil {
		return nil, err
	}
	ctx = cloudctx.WithUin(ctx, uin)

	// 获取并生成LBR相关信息
	lbr, err := Get().Cluster().GetLoadBalancerResource(ctx, cluster, lbid)
	if err != nil || lbr == nil {
		log.FromContext(ctx).Error(err, "inspection: get lbr error")
		return nil, err
	}
	lbrinfo := GenerateLBRInfo(*lbr, cluster, includeBackend)
	report.LBRInfo = &lbrinfo
	lbrinfo.ClusterID = cluster.ClusterID

	// 获取并生成CLB相关信息
	localctx := cloudctx.WithRegion(ctx, region.MustGet(lbr.Spec.Region))
	clb, err := Get().CLB().GetLoadBalancer(localctx, lbid)
	if err != nil {
		return nil, err
	}
	clbinfo := GenerateCLBInfo(*clb)
	report.CLBInfo = &clbinfo

	// 生成CLB异常数据统计信息
	statsinfo := GenerateCLBStats(localctx, *lbr)
	report.CLBStats = &statsinfo

	// 补充CLB相关信息
	CompleteDFS(lbid, cluster.Appid, region.MustGet(lbrinfo.CLBRegion).ID, report.CLBInfo)

	s.cacheStore.SetDefault(cluster.ClusterID+"/"+lbid, report)
	log.Info("update cache", "item", cluster.ClusterID+"/"+lbid)
	report.ExpireAt = time.Now().Add(5 * time.Minute).Format("2006-01-02 15:04:05")
	return report, nil
}

func isVersionLessThan(version1, version2 string) bool {
	v1, err := version.NewVersion(version1)
	if err != nil {
		return false
	}

	v2, err := version.NewVersion(version2)
	if err != nil {
		return false
	}

	return v1.LessThan(v2)
}

func (s *InspectionService) GetResourceReport(ctx context.Context, cluster *model.Cluster, resourceType, resourceNamespace, resourceName string, refreshCache bool) (*model.ResourceReport, error) {
	if !refreshCache {
		if item, expireAt, ok := s.cacheStore.GetWithExpiration(cluster.ClusterID + "/" + resourceType + "/" + resourceNamespace + "/" + resourceName); ok {
			report := item.(*model.ResourceReport)
			report.ExpireAt = expireAt.Format("2006-01-02 15:04:05")
			log.Info("get item from cache", "item", cluster.ClusterID+"/"+resourceType+"/"+resourceNamespace+"/"+resourceName)
			return report, nil
		} else {
			log.Info("cache expired or not found", "item", cluster.ClusterID+"/"+resourceType+"/"+resourceNamespace+"/"+resourceName)
		}
	}

	report := &model.ResourceReport{Template: "report_resource"}
	switch strings.ToLower(resourceType) {
	case "service", "svc":
		report.Type = "Service"
		report.ClusterID = cluster.ClusterID
		report.Namespace = resourceNamespace
		report.Name = resourceName
		obj, err := Get().Cluster().GetService(ctx, cluster, resourceNamespace, resourceName)
		if err != nil {
			return nil, err
		}
		report.CreateTime = obj.CreationTimestamp.String()
		obj.SetManagedFields(nil)
		raw, _ := yaml.Marshal(obj)
		report.Raw = string(raw)
		if GetServiceConditions(obj) {
			report.Status = "健康"
		} else {
			report.Status = "异常"
		}
		anno := ServiceAnnotations(obj.Annotations)
		report.CLBID = anno.LBID()
		report.Reused = anno.IsReused()
		if prjID, _, ok := extractNamespaceInfo(resourceNamespace); ok {
			report.TKEx = &model.TKExReference{
				TargetType: "Service",
				TargetName: resourceName,

				ClusterID: cluster.ClusterID,
				Namespace: resourceNamespace,
				ProjectID: prjID,
			}
			report.TKEx.Link = report.TKEx.BuildLink()
		}

	case "ingress", "ing":
		report.Type = "Ingress"
		report.ClusterID = cluster.ClusterID
		report.Namespace = resourceNamespace
		report.Name = resourceName
		if isVersionLessThan(cluster.K8SVersion, "1.20.0") {
			obj, err := Get().Cluster().GetExtensionIngress(ctx, cluster, resourceNamespace, resourceName)
			if err != nil {
				return nil, err
			}
			report.CreateTime = obj.CreationTimestamp.String()
			obj.SetManagedFields(nil)
			raw, _ := yaml.Marshal(obj)
			report.Raw = string(raw)
			if GetIngressConditions(obj.Annotations) {
				report.Status = "健康"
			} else {
				report.Status = "异常"
			}
			anno := IngressAnnotations(obj.Annotations)
			report.CLBID = anno.LBID()
			report.Reused = anno.IsReused()
		} else {
			obj, err := Get().Cluster().GetNetworkingIngress(ctx, cluster, resourceNamespace, resourceName)
			if err != nil {
				return nil, err
			}
			report.CreateTime = obj.CreationTimestamp.String()
			obj.SetManagedFields(nil)
			raw, _ := yaml.Marshal(obj)
			report.Raw = string(raw)
			if GetIngressConditions(obj.Annotations) {
				report.Status = "健康"
			} else {
				report.Status = "异常"
			}
			anno := IngressAnnotations(obj.Annotations)
			report.CLBID = anno.LBID()
			report.Reused = anno.IsReused()
		}

		if prjID, _, ok := extractNamespaceInfo(resourceNamespace); ok {
			report.TKEx = &model.TKExReference{
				TargetType: "Ingress",
				TargetName: resourceName,

				ClusterID: cluster.ClusterID,
				Namespace: resourceNamespace,
				ProjectID: prjID,
			}
			report.TKEx.Link = report.TKEx.BuildLink()
		}
	case "multiclusterservice", "mcs":
		report.Type = "MulticlusterService"
		report.ClusterID = cluster.ClusterID
		report.Namespace = resourceNamespace
		report.Name = resourceName
		obj, err := Get().Cluster().GetMCS(ctx, cluster, resourceNamespace, resourceName)
		if err != nil {
			return nil, err
		}
		report.CreateTime = obj.CreationTimestamp.String()
		obj.SetManagedFields(nil)
		raw, _ := yaml.Marshal(obj)
		report.Raw = string(raw)
		if GetMCSConditions(obj) {
			report.Status = "健康"
		} else {
			report.Status = "异常"
		}
		anno := ServiceAnnotations(obj.Annotations)
		report.CLBID = anno.LBID()
		report.Reused = anno.IsReused()

		if prjID, envName, ok := extractNamespaceInfo(resourceNamespace); ok {
			report.AppFabric = &model.AppFabricReference{
				TargetType:      "MulticlusterService",
				TargetName:      resourceName,
				Namespace:       resourceNamespace,
				ProjectID:       prjID,
				EnvironmentName: envName,
				CLBRegion:       obj.GetAnnotations()["service.cloud.tencent.com/cross-region-id"],

				AppID:         obj.GetLabels()["app.camp.io/application-id"],
				InstanceID:    obj.GetLabels()["app.camp.io/id"],
				ComponentName: obj.GetAnnotations()["component.app.tad.io/name"],
				TraitName:     obj.GetAnnotations()["app.tad.io/clb-Service-trait-name"],
			}
			if _, ok := obj.GetAnnotations()["app.tad.io/Specify-MultiService-name"]; ok {
				report.AppFabric.EnvironmentName = resourceNamespace
			}
			report.AppFabric.Link = report.AppFabric.BuildLink()
		}
	case "multiclusteringress", "mci":
		report.Type = "MulticlusterIngress"
		report.ClusterID = cluster.ClusterID
		report.Namespace = resourceNamespace
		report.Name = resourceName
		obj, err := Get().Cluster().GetMCI(ctx, cluster, resourceNamespace, resourceName)
		if err != nil {
			return nil, err
		}
		report.CreateTime = obj.CreationTimestamp.String()
		obj.SetManagedFields(nil)
		raw, _ := yaml.Marshal(obj)
		report.Raw = string(raw)
		if GetMCIConditions(obj) {
			report.Status = "健康"
		} else {
			report.Status = "异常"
		}
		anno := IngressAnnotations(obj.Annotations)
		report.CLBID = anno.LBID()
		report.Reused = anno.IsReused()

		if prjID, envName, ok := extractNamespaceInfo(resourceNamespace); ok {
			report.AppFabric = &model.AppFabricReference{
				TargetType:      "MulticlusterIngress",
				TargetName:      resourceName,
				Namespace:       resourceNamespace,
				ProjectID:       prjID,
				EnvironmentName: envName,
				CLBRegion:       obj.GetAnnotations()["ingress.cloud.tencent.com/cross-region-id"],
			}
			report.AppFabric.Link = report.AppFabric.BuildLink()
		}
	}

	s.cacheStore.SetDefault(cluster.ClusterID+"/"+resourceType+"/"+resourceNamespace+"/"+resourceName, report)
	log.Info("update cache", "item", cluster.ClusterID+"/"+resourceType+"/"+resourceNamespace+"/"+resourceName)
	report.ExpireAt = time.Now().Add(5 * time.Minute).Format("2006-01-02 15:04:05")
	return report, nil
}

// https://console.cloud.tencent.com/camp/app/service/info?appId=app-xrz4kvm9&projectId=prjvd49x&instanceId=tad-q8pjpmg8&envName=pre-release&serviceType=clbservice&componentName=tafregistryserver-pre&serviceName=vip

func extractNamespaceInfo(ns string) (string, string, bool) {
	if !strings.Contains(ns, "prj") {
		return "", "", false
	}
	out := strings.Split(ns, "-")
	switch len(out) {
	case 2:
		return out[0], out[1], true
	case 3:
		if out[0] == "prj" {
			return out[0] + "-" + out[1], out[2], true
		} else if strings.Contains(out[0], "prj") {
			return out[0], out[1] + "-" + out[2], true
		}
	case 4:
		return out[1], out[3], true
	}
	return "", "", false
}

// 检索 CLB 列表
func ScanCLBList(ctx context.Context, cluster *model.Cluster) ([]model.CLBRiskDetail, error) {
	// 获取集群的LBR列表
	lbrlist, err := Get().Cluster().ListLBR(ctx, cluster)
	if err != nil {
		log.FromContext(ctx).Error(err, "inspection: list lbr error")
		return nil, err
	}
	// 合并所有分组的结果
	output := []model.CLBRiskDetail{}
	for _, lbr := range lbrlist {
		lbrinfo := GenerateLBRInfo(lbr, cluster, false)
		output = append(output, model.CLBRiskDetail{
			CLBID:     lbr.Name,
			LBRInfo:   &lbrinfo,
			Protocols: UsingProtocols(lbr),
			Weight:    &model.RSWeightDetail{},
			Health:    &model.RSHealthDetail{},
		})
	}
	return output, nil
}

// 检索存在风险的CLB列表
func ScanRiskCLBList(ctx context.Context, cluster *model.Cluster) ([]model.CLBRiskDetail, error) {
	// 获取集群的LBR列表
	lbrlist, err := Get().Cluster().ListLBR(ctx, cluster)
	if err != nil {
		log.FromContext(ctx).Error(err, "inspection: list lbr error")
		return nil, err
	}

	// 考虑大集群分组，分批并发，控制并发度
	groups := SplitLBRGroups(lbrlist, config.Get().Inspection.MaxConcurrentSyncLBRPerCluster)
	results := [][]model.CLBRiskDetail{}
	groupsSyncTime := time.Now()
	// 遍历每个分组
	for index, group := range groups {
		groupSyncTime := time.Now()
		var wg sync.WaitGroup
		details := make([]model.CLBRiskDetail, len(group))
		// 并发处理每个LBR
		for i, lbr := range group {
			wg.Add(1)
			go func(ctx context.Context, i int, lbr lbrv1alpha1.LoadBalancerResource) {
				defer wg.Done()

				// 为每个LBR创建带有区域信息的上下文
				localctx := cloudctx.WithRegion(ctx, region.MustGet(lbr.Spec.Region))
				lbrinfo := GenerateLBRInfo(lbr, cluster, false)
				detail := model.CLBRiskDetail{
					CLBID:   lbr.Name,
					LBRInfo: &lbrinfo,
				}
				// 获取RS的权重信息
				weight, err := ListRSWeight(localctx, lbr)
				if err == nil {
					if weight.TotalRSCount == 0 {
						detail.IsZeroRealServer = true
					}
					detail.Weight = weight
					detail.Protocols = weight.Protocols
				} else {
					if strings.Contains(err.Error(), "LoadBalancer not exist") {
						detail.IsNotFound = true
					} else {
						log.FromContext(localctx).Error(err, "inspection: list rs weight data error", "lbid", lbr.Name)
						return
					}
				}
				// 如果RS存在，获取RS的健康信息
				if !detail.IsNotFound {
					health, err := ListRSHealth(localctx, lbr)
					if err == nil {
						if health.TotalRSCount == 0 {
							detail.IsZeroRealServer = true
						}
						detail.Health = health
						detail.Protocols = health.Protocols
					} else {
						if strings.Contains(err.Error(), "Loadbalancers not exist") {
							detail.IsNotFound = true
						} else {
							log.FromContext(localctx).Error(err, "inspection: list rs health data error", "lbid", lbr.Name)
							return
						}
					}
				}
				// 将结果存储到对应的位置
				details[i] = detail
			}(ctx, i, lbr)
		}
		// 等待所有并发任务完成
		wg.Wait()

		// 将当前分组的结果追加到总结果中
		results = append(results, details)
		// 打印当前批次的进度和耗时
		if err := ctx.Err(); err != nil {
			log.FromContext(ctx).Error(err, "健康巡检异常", "clusterid", cluster.ClusterID, "当前批次", index+1, "进度", fmt.Sprintf("%.2f%%", 100*(float64(index+1)/float64(len(groups)))))
			return nil, err
		} else {
			log.FromContext(ctx).Info("健康巡检", "clusterid", cluster.ClusterID, "当前批次", index+1, "进度", fmt.Sprintf("%.2f%%", 100*(float64(index+1)/float64(len(groups)))), "单次耗时", time.Since(groupSyncTime).String())
		}
	}
	// 打印总的扫描完成信息，包括总批次、总耗时、集群ID和并发度
	log.FromContext(ctx).Info("健康巡检完成", "clusterid", cluster.ClusterID, "总批次", len(groups), "总耗时", time.Since(groupsSyncTime).String(), "集群ID", cluster.ClusterID, "并发度", config.Get().Inspection.MaxConcurrentSyncLBRPerCluster)

	// 合并所有分组的结果
	output := []model.CLBRiskDetail{}
	for _, merge := range results {
		output = append(output, merge...)
	}
	return output, nil
}

// 基于风险CLB生成统计数据
func GenerateStats(clbDetails []model.CLBRiskDetail) model.ClusterHealthStatistics {
	var (
		totalRSCount       int // 总的RealServer数量
		totalListenerCount int // 总的Listener数量
		totalRuleCount     int // 总的Rule数量
	)
	// 初始化统计结构体
	stats := model.ClusterHealthStatistics{
		CLB: model.CLBRStats{
			TotalCount: uint(len(clbDetails)), // CLB的总数
		},
	}
	// 遍历每个CLB的详细信息
	for _, clb := range clbDetails {
		if clb.IsNotFound {
			stats.CLB.TotalNotExistedCount++
			continue
		}
		if clb.IsZeroRealServer {
			stats.CLB.TotalNoRealServerCount++
			continue
		}
		if clb.Weight != nil {
			// 累加总的RealServer、Listener和Rule数量
			totalRSCount += clb.Weight.TotalRSCount
			totalListenerCount += clb.Weight.TotalListenerCount
			totalRuleCount += clb.Weight.TotalRuleCount

			// 如果总的RealServer数量等于总的禁止RealServer数量且不为0，则增加总的无权重RealServer计数
			if clb.Weight.TotalRSCount == clb.Weight.TotalForbiddenRSCount && clb.Weight.TotalForbiddenRSCount != 0 {
				stats.CLB.TotalZeroWeightCount++
			}
			// 累加总的禁止Listener和Rule数量
			stats.Listener.TotalZeroWeightCount += uint(clb.Weight.TotalAllDownlistenerCount)
			stats.Rule.TotalZeroWeightCount += uint(clb.Weight.TotalAllDownRuleCount)
			// 累加总的禁止RealServer数量
			stats.RealServer.TotalZeroWeightCount += uint(clb.Weight.TotalForbiddenRSCount)
		}
		if clb.Health != nil {
			// 如果总的RealServer数量等于总的禁止RealServer数量且不为0，则增加总的不健康RealServer计数
			if clb.Health.TotalRSCount == clb.Health.TotalForbiddenRSCount && clb.Health.TotalForbiddenRSCount != 0 {
				stats.CLB.TotalUnhealthCount++
			}
			// 累加总的禁止Listener和Rule数量
			stats.Listener.TotalUnhealthCount += uint(clb.Health.TotalAllDownlistenerCount)
			stats.Rule.TotalUnhealthCount += uint(clb.Health.TotalAllDownRuleCount)
			// 累加总的禁止RealServer数量
			stats.RealServer.TotalUnhealthCount += uint(clb.Health.TotalForbiddenRSCount)
		}
	}
	// 设置总的RealServer、Listener和Rule数量
	stats.RealServer.TotalCount = uint(totalRSCount)
	stats.Rule.TotalCount = uint(totalRuleCount)
	stats.Listener.TotalCount = uint(totalListenerCount)
	return stats // 返回统计结果
}

// 基于风险CLB生成分析数据
func GenerateAnalysises(details []model.CLBRiskDetail, containNoRisk bool) model.ClusterHealthAnalysises {
	analysises := map[string]model.CLBRisks{}
	for _, detail := range details {
		var (
			reasons        = map[model.Risk]int{}
			protocols      string
			usingResources string
		)
		usingResources = detail.ResourceInfo()
		if detail.Health != nil {
			protocols = detail.Protocols
			if detail.Health.TotalForbiddenRSCount == detail.Health.TotalRSCount && detail.Health.TotalRSCount != 0 {
				reasons[model.AllDownCLBByHealth]++
			}
			if detail.Health.TotalAllDownlistenerCount > 0 {
				reasons[model.HasAllDownListenerByHealth] = detail.Health.TotalAllDownlistenerCount
			}
			if detail.Health.TotalAllDownRuleCount > 0 {
				reasons[model.HasAlDownRuleByHealth] = detail.Health.TotalAllDownRuleCount
			}
			if detail.Health.TotalForbiddenRSCount > 0 {
				reasons[model.HasDownRSByHealth] = detail.Health.TotalForbiddenRSCount
			}
		}
		if detail.Weight != nil {
			protocols = detail.Protocols
			if detail.Weight.TotalForbiddenRSCount == detail.Weight.TotalRSCount && detail.Weight.TotalRSCount != 0 {
				reasons[model.AllDownCLBByWeight]++
			}
			if detail.Weight.TotalAllDownlistenerCount > 0 {
				reasons[model.HasAllDownListenerByWeight] = detail.Weight.TotalAllDownlistenerCount
			}
			if detail.Weight.TotalAllDownRuleCount > 0 {
				reasons[model.HasAllDownRuleByWeight] = detail.Weight.TotalAllDownRuleCount
			}
			if detail.Weight.TotalForbiddenRSCount > 0 {
				reasons[model.HasDownRSByWeight] = detail.Weight.TotalForbiddenRSCount
			}
		}

		target, ok := analysises[detail.CLBID]
		if ok {
			if protocols != "" {
				target.Protocols = protocols
			}
			for reason, counter := range reasons {
				target.Risks[reason] = target.Risks[reason] + counter
			}
			if usingResources != "" {
				target.UsingResources = usingResources
			}
		} else {
			if protocols != "" {
				target.Protocols = protocols
			}
			if usingResources != "" {
				target.UsingResources = usingResources
			}
			target.Risks = reasons
		}
		analysises[detail.CLBID] = target
	}
	result := map[string]model.CLBRisks{}
	for key, reasonMap := range analysises {
		if len(reasonMap.Risks) != 0 || containNoRisk {
			result[key] = reasonMap
		}
	}

	resultDetails := []model.CLBRiskDetail{}
	var (
		totalCLBLevelRiskCount      int
		totalListenerLevelRiskCount int
		totalRuleLevelRiskCount     int
		totalRSLevelRiskCount       int
	)
	for key, reasons := range result {
		clbCount, listenerCount, ruleCount, rsCount := reasons.RiskCount()
		if clbCount > 0 {
			totalCLBLevelRiskCount++
		} else if listenerCount > 0 {
			totalListenerLevelRiskCount++
		} else if ruleCount > 0 {
			totalRuleLevelRiskCount++
		} else if rsCount > 0 {
			totalRSLevelRiskCount++
		}

		resultDetails = append(resultDetails, model.CLBRiskDetail{
			CLBID:          key,
			Protocols:      reasons.Protocols,
			RiskLevel:      reasons.TopRiskLevel(),
			RiskInfos:      reasons.Strings(),
			RiskScore:      reasons.GetScores(),
			Counters:       reasons.Counters(),
			RiskCount:      uint(len(reasons.Risks)),
			UsingResources: reasons.UsingResources,
		})
	}

	sort.Slice(resultDetails, func(i, j int) bool {
		return resultDetails[i].RiskScore > resultDetails[j].RiskScore
	})

	short := model.ClusterHealthAnalysises{
		TotalCLBLevelRiskCount:      totalCLBLevelRiskCount,
		TotalListenerLevelRiskCount: totalListenerLevelRiskCount,
		TotalRuleLevelRiskCount:     totalRuleLevelRiskCount,
		TotalRSLevelRiskCount:       totalRSLevelRiskCount,
		Details:                     resultDetails,
	}
	return short
}

func GenerateCLBStats(localctx context.Context, lbr lbrv1alpha1.LoadBalancerResource) (clbstats model.CLBHealthStats) {
	var (
		stats     = []model.CLBRisk{} // 存储CLB风险的切片
		protocols string              // 存储协议类型
	)

	// 获取RS权重信息
	weight, err := ListRSWeight(localctx, lbr)
	if err == nil {
		protocols = weight.Protocols // 设置协议类型
		// 构建权重统计信息并追加到stats切片中
		stats = append(stats, BuildWeightStats(weight.FailedListenerSet,
			weight.FailedRuleSet,
			weight.PartFailedListenerSet,
			weight.PartFailedRuleSet)...)

		// 设置CLB健康统计信息的各项指标
		clbstats.TotalListenerCount = weight.TotalListenerCount
		clbstats.TotalRuleCount = weight.TotalRuleCount
		clbstats.TotalRSCount = weight.TotalRSCount
		clbstats.TotalZeroWeightForbiddenRSCount = weight.TotalForbiddenRSCount
		clbstats.TotalAllDownlistenerCountByWeight = weight.TotalAllDownlistenerCount
		clbstats.TotalAllDownRuleCountByWeight = weight.TotalAllDownRuleCount
	} else {
		// 如果获取RS权重信息失败，记录错误日志
		log.FromContext(localctx).Error(err, "inspection: list rs weight data error", "lbid", lbr.Name)
	}

	// 获取RS健康信息
	health, err := ListRSHealth(localctx, lbr)
	if err == nil {
		protocols = health.Protocols // 设置协议类型
		// 构建健康统计信息并追加到stats切片中
		stats = append(stats, BuildHealthStats(health.FailedListenerSet,
			health.FailedRuleSet,
			health.PartFailedListenerSet,
			health.PartFailedRuleSet)...)

		// 设置CLB健康统计信息的各项指标
		clbstats.TotalListenerCount = health.TotalListenerCount
		clbstats.TotalRuleCount = health.TotalRuleCount
		clbstats.TotalRSCount = health.TotalRSCount
		clbstats.TotalUnhealthForbiddenRSCount = health.TotalForbiddenRSCount
		clbstats.TotalAllDownRuleCountByHealth = health.TotalAllDownRuleCount
		clbstats.TotalAllDownlistenerCountByHealth = health.TotalAllDownlistenerCount
	} else {
		// 如果获取RS健康信息失败，记录错误日志
		log.FromContext(localctx).Error(err, "inspection: list rs health data error", "lbid", lbr.Name)
	}

	// 设置最终的统计信息和使用的协议类型
	clbstats.Stats = stats
	clbstats.UsingProtocols = protocols

	return clbstats // 返回CLB健康统计信息
}

func ListRSWeight(ctx context.Context, lbr lbrv1alpha1.LoadBalancerResource) (*model.RSWeightDetail, error) {
	protocols := sets.NewString()

	clbBackendWeightMap := make(map[string]*clb.ListenerBackend)
	clbBackendWeights, err := Get().CLB().ListListenerBackends(ctx, lbr.Name)
	if err != nil {
		return nil, err
	}
	if clbBackendWeights == nil {
		return nil, nil
	}
	for _, listenerBackend := range clbBackendWeights {
		listenerKey := getListenerKey(*listenerBackend.Port, *listenerBackend.Protocol)
		clbBackendWeightMap[listenerKey] = listenerBackend
	}
	var (
		totalRSCount              int
		totalForbiddenRSCount     int
		totalListenerCount        int
		totalAllDownlistenerCount int
		totalRuleCount            int
		totalAllDownRuleCount     int

		failedListenerSet = sets.NewString()
		failedRuleSet     = sets.NewString()

		partFailedListenerSet = sets.NewString()
		partFailedRuleSet     = sets.NewString()
	)
	for _, listener := range lbr.Spec.Listeners {
		protocols.Insert(listener.Protocol)

		var (
			listenerRSCount int
		)
		listenerKey := getListenerKey(int64(listener.Port), listener.Protocol)
		rsMap := make(map[string][]*clb.Backend)
		ruleList, exist := clbBackendWeightMap[listenerKey]
		if !exist {
			continue
		}
		totalListenerCount++
		var hasAliveInListenerRS = false

		if isL4Protocol(listener.Protocol) {
			rsMap[""] = ruleList.Targets
		} else {
			for _, rule := range ruleList.Rules {
				totalRuleCount++
				ruleKey := listenerKey + "=" + getRuleKey(*rule.Domain, *rule.Url)
				if rsMap[ruleKey] == nil {
					rsMap[ruleKey] = make([]*clb.Backend, 0)
				}
				rsMap[ruleKey] = append(rsMap[ruleKey], rule.Targets...)
			}
		}

		for rulekey, rsList := range rsMap {
			var ruleRSCount int
			var hasAliveInRuleRS = false
			for _, rs := range rsList {
				if rulekey != "" {
					ruleRSCount = ruleRSCount + 1
					listenerRSCount = listenerRSCount + 1
				} else {
					listenerRSCount = listenerRSCount + 1
				}
				totalRSCount = totalRSCount + 1
				if rs.Weight != nil && *rs.Weight != 0 {
					hasAliveInListenerRS = true
					hasAliveInRuleRS = true
				} else if !ignorableZeroWeightRS(ctx, lbr.Name, rs.RegisteredTime) {
					if rulekey != "" {
						partFailedRuleSet.Insert(rulekey)
					} else {
						partFailedListenerSet.Insert(listenerKey)
					}
					totalForbiddenRSCount = totalForbiddenRSCount + 1
				}
			}

			if rulekey != "" && !hasAliveInRuleRS && ruleRSCount != 0 {
				failedRuleSet.Insert(rulekey)
				totalAllDownRuleCount++
			}
		}
		if !hasAliveInListenerRS && listenerRSCount != 0 {
			failedListenerSet.Insert(listenerKey)
			totalAllDownlistenerCount++
		}
	}

	partFailedListenerSet.Delete(failedListenerSet.List()...)
	partFailedRuleSet.Delete(failedRuleSet.List()...)
	return &model.RSWeightDetail{
		Protocols:                 strings.Join(protocols.List(), ","),
		FailedListenerSet:         failedListenerSet,
		FailedRuleSet:             failedRuleSet,
		PartFailedListenerSet:     partFailedListenerSet,
		PartFailedRuleSet:         partFailedRuleSet,
		TotalRSCount:              totalRSCount,
		TotalForbiddenRSCount:     totalForbiddenRSCount,
		TotalListenerCount:        totalListenerCount,
		TotalAllDownlistenerCount: totalAllDownlistenerCount,
		TotalRuleCount:            totalRuleCount,
		TotalAllDownRuleCount:     totalAllDownRuleCount,
	}, nil
}

func ListRSHealth(ctx context.Context, lbr lbrv1alpha1.LoadBalancerResource) (*model.RSHealthDetail, error) {
	protocols := sets.NewString()

	clbBackendHealthMap := make(map[string][]*clb.RuleHealth)
	clbBackendHealths, err := Get().CLB().ListListenerBackendHealth(ctx, lbr.Name)
	if err != nil {
		return nil, err
	}
	if clbBackendHealths == nil {
		return nil, nil
	}
	for _, listenerHealth := range clbBackendHealths.Listeners {
		listenerKey := getListenerKey(*listenerHealth.Port, *listenerHealth.Protocol)
		clbBackendHealthMap[listenerKey] = listenerHealth.Rules
	}
	var (
		totalRSCount              int
		totalForbiddenRSCount     int
		totalListenerCount        int
		totalAllDownlistenerCount int
		totalRuleCount            int
		totalAllDownRuleCount     int

		failedListenerSet = sets.NewString()
		failedRuleSet     = sets.NewString()

		partFailedListenerSet = sets.NewString()
		partFailedRuleSet     = sets.NewString()
	)
	for _, listener := range lbr.Spec.Listeners {
		protocols.Insert(listener.Protocol)
		var (
			listenerRSCount int
		)
		listenerKey := getListenerKey(int64(listener.Port), listener.Protocol)
		healthMap := make(map[string][]*clb.RuleHealth)
		ruleList, exist := clbBackendHealthMap[listenerKey]
		if !exist {
			continue
		}
		totalListenerCount++
		var hasAliveInListenerRS = false

		if isL4Protocol(listener.Protocol) {
			healthMap[""] = ruleList
		} else {
			for index, rule := range ruleList {
				totalRuleCount++
				ruleKey := listenerKey + "=" + getRuleKey(*rule.Domain, *rule.Url)
				if healthMap[ruleKey] == nil {
					healthMap[ruleKey] = make([]*clb.RuleHealth, 0)
				}
				healthMap[ruleKey] = append(healthMap[ruleKey], ruleList[index])
			}
		}

		for rulekey, healthList := range healthMap {
			for _, rule := range healthList {
				var ruleRSCount int
				var hasAliveInRuleRS = false
				for _, target := range rule.Targets {
					if rulekey != "" {
						ruleRSCount = ruleRSCount + 1
						listenerRSCount = listenerRSCount + 1
					} else {
						listenerRSCount = listenerRSCount + 1
					}
					totalRSCount = totalRSCount + 1
					// "Unknown" 探测中
					if *target.HealthStatusDetail == "Alive" || *target.HealthStatusDetail == "Close" || *target.HealthStatusDetail == "Unknown" {
						hasAliveInListenerRS = true
						hasAliveInRuleRS = true
					} else {
						if rulekey != "" {
							partFailedRuleSet.Insert(rulekey)
						} else {
							partFailedListenerSet.Insert(listenerKey)
						}
						totalForbiddenRSCount = totalForbiddenRSCount + 1
					}
				}

				if rulekey != "" && !hasAliveInRuleRS && ruleRSCount != 0 {
					failedRuleSet.Insert(rulekey)
					totalAllDownRuleCount++
				}
			}
		}
		if !hasAliveInListenerRS && listenerRSCount != 0 {
			failedListenerSet.Insert(listenerKey)
			totalAllDownlistenerCount++
		}
	}

	partFailedListenerSet.Delete(failedListenerSet.List()...)
	partFailedRuleSet.Delete(failedRuleSet.List()...)
	return &model.RSHealthDetail{
		Protocols:                 strings.Join(protocols.List(), ","),
		FailedListenerSet:         failedListenerSet,
		FailedRuleSet:             failedRuleSet,
		PartFailedListenerSet:     partFailedListenerSet,
		PartFailedRuleSet:         partFailedRuleSet,
		TotalRSCount:              totalRSCount,
		TotalForbiddenRSCount:     totalForbiddenRSCount,
		TotalListenerCount:        totalListenerCount,
		TotalAllDownlistenerCount: totalAllDownlistenerCount,
		TotalRuleCount:            totalRuleCount,
		TotalAllDownRuleCount:     totalAllDownRuleCount,
	}, nil
}
