#!/usr/bin/env python3
"""
Risk data analysis script for cluster inspection results
Usage: python3 analyze_risk_data.py [aggregated_risk_analysis.json]
"""

import json
import sys
import os
from collections import defaultdict, Counter
import argparse

def load_aggregated_data(file_path):
    """Load aggregated risk analysis data"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"Error: File {file_path} not found")
        return None
    except json.JSONDecodeError:
        print(f"Error: Invalid JSON in {file_path}")
        return None

def analyze_risk_patterns(data):
    """Analyze risk patterns and generate insights"""
    analysis = {
        'cluster_analysis': {},
        'namespace_analysis': {},
        'resource_analysis': {},
        'lb_analysis': {},
        'cross_cluster_patterns': {},
        'recommendations': []
    }
    
    # Analyze clusters
    for cluster_id, ingress_list in data['risk_ingress']['by_cluster'].items():
        if cluster_id not in analysis['cluster_analysis']:
            analysis['cluster_analysis'][cluster_id] = {
                'risk_ingress_count': 0,
                'risk_service_count': 0,
                'total_risk_count': 0,
                'risk_score': 0
            }
        analysis['cluster_analysis'][cluster_id]['risk_ingress_count'] = len(ingress_list)
    
    for cluster_id, service_list in data['risk_service']['by_cluster'].items():
        if cluster_id not in analysis['cluster_analysis']:
            analysis['cluster_analysis'][cluster_id] = {
                'risk_ingress_count': 0,
                'risk_service_count': 0,
                'total_risk_count': 0,
                'risk_score': 0
            }
        analysis['cluster_analysis'][cluster_id]['risk_service_count'] = len(service_list)
    
    # Calculate total risk counts and scores
    for cluster_id, cluster_data in analysis['cluster_analysis'].items():
        cluster_data['total_risk_count'] = cluster_data['risk_ingress_count'] + cluster_data['risk_service_count']
        # Simple risk scoring: each ingress risk = 2 points, each service risk = 1 point
        cluster_data['risk_score'] = cluster_data['risk_ingress_count'] * 2 + cluster_data['risk_service_count']
    
    # Analyze namespaces
    namespace_counter = Counter()
    resource_counter = Counter()
    lb_counter = Counter()
    
    # Process ingress data
    for entry in data['risk_ingress']['all_entries']:
        try:
            parts = entry['entry'].split('/')
            if len(parts) >= 8:
                namespace = parts[3]
                resource_name = parts[5]
                lb_id = parts[7]
                
                namespace_counter[namespace] += 1
                resource_counter[f"ingress:{namespace}/{resource_name}"] += 1
                lb_counter[lb_id] += 1
        except:
            continue
    
    # Process service data
    for entry in data['risk_service']['all_entries']:
        try:
            parts = entry['entry'].split('/')
            if len(parts) >= 8:
                namespace = parts[3]
                resource_name = parts[5]
                lb_id = parts[7]
                
                namespace_counter[namespace] += 1
                resource_counter[f"service:{namespace}/{resource_name}"] += 1
                lb_counter[lb_id] += 1
        except:
            continue
    
    analysis['namespace_analysis'] = dict(namespace_counter.most_common())
    analysis['resource_analysis'] = dict(resource_counter.most_common())
    analysis['lb_analysis'] = dict(lb_counter.most_common())
    
    # Analyze cross-cluster patterns
    ingress_patterns = data['risk_ingress']['grouped_by_pattern']
    service_patterns = data['risk_service']['grouped_by_pattern']
    
    for pattern, entries in ingress_patterns.items():
        if len(entries) > 1:  # Pattern appears in multiple clusters
            analysis['cross_cluster_patterns'][f"ingress:{pattern}"] = {
                'type': 'ingress',
                'pattern': pattern,
                'cluster_count': len(set(entry['cluster_id'] for entry in entries)),
                'total_occurrences': len(entries),
                'clusters': list(set(entry['cluster_id'] for entry in entries))
            }
    
    for pattern, entries in service_patterns.items():
        if len(entries) > 1:  # Pattern appears in multiple clusters
            analysis['cross_cluster_patterns'][f"service:{pattern}"] = {
                'type': 'service',
                'pattern': pattern,
                'cluster_count': len(set(entry['cluster_id'] for entry in entries)),
                'total_occurrences': len(entries),
                'clusters': list(set(entry['cluster_id'] for entry in entries))
            }
    
    # Generate recommendations
    recommendations = []
    
    # High-risk clusters
    high_risk_clusters = [
        (cluster_id, data) for cluster_id, data in analysis['cluster_analysis'].items()
        if data['risk_score'] >= 5
    ]
    if high_risk_clusters:
        recommendations.append({
            'priority': 'HIGH',
            'category': 'Cluster Risk',
            'description': f"Found {len(high_risk_clusters)} high-risk clusters with risk score >= 5",
            'details': [f"{cluster_id} (score: {data['risk_score']})" for cluster_id, data in high_risk_clusters[:5]],
            'action': 'Review and fix pass-to-target configurations in these clusters'
        })
    
    # Frequent namespaces
    frequent_namespaces = [(ns, count) for ns, count in namespace_counter.most_common(5) if count >= 3]
    if frequent_namespaces:
        recommendations.append({
            'priority': 'MEDIUM',
            'category': 'Namespace Pattern',
            'description': f"Found {len(frequent_namespaces)} namespaces with multiple risk entries",
            'details': [f"{ns} ({count} entries)" for ns, count in frequent_namespaces],
            'action': 'Review namespace-level configurations and policies'
        })
    
    # Cross-cluster patterns
    cross_cluster_count = len(analysis['cross_cluster_patterns'])
    if cross_cluster_count > 0:
        recommendations.append({
            'priority': 'MEDIUM',
            'category': 'Cross-Cluster Pattern',
            'description': f"Found {cross_cluster_count} resource patterns appearing across multiple clusters",
            'details': [f"{pattern} (in {data['cluster_count']} clusters)" 
                       for pattern, data in list(analysis['cross_cluster_patterns'].items())[:5]],
            'action': 'Consider standardizing configurations across clusters'
        })
    
    # Load balancer reuse
    frequent_lbs = [(lb, count) for lb, count in lb_counter.most_common(5) if count >= 3]
    if frequent_lbs:
        recommendations.append({
            'priority': 'LOW',
            'category': 'Load Balancer Usage',
            'description': f"Found {len(frequent_lbs)} load balancers with multiple risk entries",
            'details': [f"{lb} ({count} entries)" for lb, count in frequent_lbs],
            'action': 'Review load balancer configurations and resource allocation'
        })
    
    analysis['recommendations'] = recommendations
    return analysis

def generate_report(data, analysis, output_file=None):
    """Generate a comprehensive analysis report"""
    report_lines = []
    
    # Header
    report_lines.extend([
        "=" * 80,
        "CLUSTER RISK ANALYSIS REPORT",
        "=" * 80,
        f"Generated at: {os.popen('date').read().strip()}",
        ""
    ])
    
    # Summary
    summary = data['summary']
    report_lines.extend([
        "EXECUTIVE SUMMARY",
        "-" * 40,
        f"Total clusters analyzed: {summary['total_clusters']}",
        f"Clusters with RiskIngress: {summary['clusters_with_risk_ingress']} ({summary['clusters_with_risk_ingress']/summary['total_clusters']*100:.1f}%)",
        f"Clusters with RiskService: {summary['clusters_with_risk_service']} ({summary['clusters_with_risk_service']/summary['total_clusters']*100:.1f}%)",
        f"Total risk entries: {summary['total_risk_ingress_count'] + summary['total_risk_service_count']}",
        f"  - RiskIngress: {summary['total_risk_ingress_count']}",
        f"  - RiskService: {summary['total_risk_service_count']}",
        ""
    ])
    
    # Top risk clusters
    top_clusters = sorted(analysis['cluster_analysis'].items(), 
                         key=lambda x: x[1]['risk_score'], reverse=True)[:10]
    if top_clusters:
        report_lines.extend([
            "TOP RISK CLUSTERS",
            "-" * 40,
        ])
        for i, (cluster_id, cluster_data) in enumerate(top_clusters, 1):
            report_lines.append(
                f"{i:2d}. {cluster_id} (Score: {cluster_data['risk_score']}, "
                f"Ingress: {cluster_data['risk_ingress_count']}, "
                f"Service: {cluster_data['risk_service_count']})"
            )
        report_lines.append("")
    
    # Namespace analysis
    if analysis['namespace_analysis']:
        report_lines.extend([
            "NAMESPACE ANALYSIS",
            "-" * 40,
        ])
        for i, (namespace, count) in enumerate(list(analysis['namespace_analysis'].items())[:10], 1):
            report_lines.append(f"{i:2d}. {namespace}: {count} risk entries")
        report_lines.append("")
    
    # Cross-cluster patterns
    if analysis['cross_cluster_patterns']:
        report_lines.extend([
            "CROSS-CLUSTER PATTERNS",
            "-" * 40,
        ])
        for pattern, data in list(analysis['cross_cluster_patterns'].items())[:10]:
            report_lines.append(
                f"• {data['type'].upper()}: {data['pattern']} "
                f"(appears in {data['cluster_count']} clusters, {data['total_occurrences']} total)"
            )
        report_lines.append("")
    
    # Recommendations
    if analysis['recommendations']:
        report_lines.extend([
            "RECOMMENDATIONS",
            "-" * 40,
        ])
        for i, rec in enumerate(analysis['recommendations'], 1):
            report_lines.extend([
                f"{i}. [{rec['priority']}] {rec['category']}",
                f"   {rec['description']}",
                f"   Action: {rec['action']}",
                ""
            ])
    
    report_content = "\n".join(report_lines)
    
    if output_file:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(report_content)
        print(f"Analysis report saved to: {output_file}")
    else:
        print(report_content)

def main():
    parser = argparse.ArgumentParser(description='Analyze cluster risk data')
    parser.add_argument('input_file', nargs='?', 
                       default='cluster_inspection_results/aggregated_risk_analysis.json',
                       help='Path to aggregated risk analysis JSON file')
    parser.add_argument('--output', '-o', 
                       help='Output file for analysis report')
    parser.add_argument('--json', action='store_true',
                       help='Output analysis results as JSON')
    
    args = parser.parse_args()
    
    # Load data
    data = load_aggregated_data(args.input_file)
    if not data:
        sys.exit(1)
    
    # Perform analysis
    analysis = analyze_risk_patterns(data)
    
    if args.json:
        # Output as JSON
        output_file = args.output or 'risk_analysis_detailed.json'
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(analysis, f, indent=2, ensure_ascii=False)
        print(f"Detailed analysis saved to: {output_file}")
    else:
        # Generate text report
        output_file = args.output or 'risk_analysis_report.txt'
        generate_report(data, analysis, output_file)

if __name__ == "__main__":
    main()
