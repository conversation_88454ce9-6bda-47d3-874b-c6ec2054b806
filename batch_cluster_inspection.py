#!/usr/bin/env python3
"""
Batch script to call cluster inspection API for multiple cluster IDs
Usage: python3 batch_cluster_inspection.py
"""

import requests
import json
import os
import time
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed
import argparse

# Configuration
BASE_URL = "http://kateway.woa.com/inspection/cluster/info/get"
FILTER = "clb"
REFRESH_CACHE = "true"
OUTPUT_DIR = "cluster_inspection_results"

# Cluster IDs list
CLUSTER_IDS = [
    "cls-pnocuy7o", "cls-8s9918je", "cls-mteeijug", "cls-2nyavit7",
    "cls-0sf7xq8h", "cls-e6antxq2", "cls-019ctobc", "cls-c3mtb8tw",
    "cls-okzcm7mk", "cls-0fvw4ql7", "cls-m29ot7ga", "cls-kbj8dces",
    "cls-97tqtjxu", "cls-ky7xdrzx", "cls-glmxm5i6", "cls-nsftw5og",
    "cls-irbazn32", "cls-242vz9pu", "cls-65s6l2y8", "cls-h05ox5x2",
    "cls-dazrr6e2", "cls-isccrwql", "cls-7gvwytdd", "cls-mi9lwsrb",
    "cls-371qkhf6", "cls-la3q70cx", "cls-mper5yyj", "cls-3c4eezcs",
    "cls-jcb5w9dm", "cls-rjzry0t9", "cls-13j49wqb", "cls-quti290e",
    "cls-5ycseuoq", "cls-bg6nw5ip", "cls-mqmg9agh", "cls-jubv4ejf",
    "cls-2bvoyng8", "cls-jrb1523h", "cls-gkohai36", "cls-qvk25nxu",
    "cls-43begdw1", "cls-qgrg0pcj", "cls-hs3tnhee", "cls-n4qoxw50",
    "cls-4aznd6j3", "cls-2300v0c4"
]

def setup_output_directory():
    """Create output directory if it doesn't exist"""
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    return os.path.join(OUTPUT_DIR, "batch_execution.log")

def log_message(message, log_file=None):
    """Log message to console and file"""
    print(message)
    if log_file:
        with open(log_file, 'a', encoding='utf-8') as f:
            f.write(f"{message}\n")

def call_cluster_api(cluster_id, timeout=30):
    """Call the API for a single cluster ID"""
    url = f"{BASE_URL}?clusterID={cluster_id}&filter={FILTER}&refreshCache={REFRESH_CACHE}"
    
    try:
        response = requests.get(url, timeout=timeout)
        return {
            'cluster_id': cluster_id,
            'status_code': response.status_code,
            'success': response.status_code == 200,
            'data': response.text,
            'url': url,
            'error': None
        }
    except requests.exceptions.RequestException as e:
        return {
            'cluster_id': cluster_id,
            'status_code': None,
            'success': False,
            'data': None,
            'url': url,
            'error': str(e)
        }

def save_result(result):
    """Save individual result to file"""
    output_file = os.path.join(OUTPUT_DIR, f"{result['cluster_id']}_result.json")
    
    if result['success'] and result['data']:
        try:
            # Try to parse as JSON to validate
            json_data = json.loads(result['data'])
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(json_data, f, indent=2, ensure_ascii=False)
            return True, "Valid JSON saved"
        except json.JSONDecodeError:
            # Save as text if not valid JSON
            with open(output_file.replace('.json', '.txt'), 'w', encoding='utf-8') as f:
                f.write(result['data'])
            return True, "Non-JSON response saved as text"
    else:
        # Save error information
        error_file = output_file.replace('.json', '_error.txt')
        with open(error_file, 'w', encoding='utf-8') as f:
            f.write(f"Error for cluster {result['cluster_id']}:\n")
            f.write(f"URL: {result['url']}\n")
            f.write(f"Status Code: {result['status_code']}\n")
            f.write(f"Error: {result['error']}\n")
        return False, f"Error saved to {error_file}"

def main():
    parser = argparse.ArgumentParser(description='Batch cluster inspection API caller')
    parser.add_argument('--parallel', '-p', type=int, default=5, 
                       help='Number of parallel requests (default: 5)')
    parser.add_argument('--timeout', '-t', type=int, default=30,
                       help='Request timeout in seconds (default: 30)')
    parser.add_argument('--delay', '-d', type=float, default=0.1,
                       help='Delay between requests in seconds (default: 0.1)')
    
    args = parser.parse_args()
    
    # Setup
    log_file = setup_output_directory()
    start_time = datetime.now()
    
    # Clear previous log
    with open(log_file, 'w') as f:
        f.write("")
    
    log_message(f"Starting batch cluster inspection at {start_time}", log_file)
    log_message(f"Total clusters to process: {len(CLUSTER_IDS)}", log_file)
    log_message(f"Parallel requests: {args.parallel}", log_file)
    log_message(f"Request timeout: {args.timeout}s", log_file)
    log_message("-" * 50, log_file)
    
    # Process clusters
    success_count = 0
    error_count = 0
    
    if args.parallel > 1:
        # Parallel processing
        with ThreadPoolExecutor(max_workers=args.parallel) as executor:
            # Submit all tasks
            future_to_cluster = {
                executor.submit(call_cluster_api, cluster_id, args.timeout): cluster_id 
                for cluster_id in CLUSTER_IDS
            }
            
            # Process completed tasks
            for i, future in enumerate(as_completed(future_to_cluster), 1):
                result = future.result()
                cluster_id = result['cluster_id']
                
                log_message(f"[{i}/{len(CLUSTER_IDS)}] Processing cluster: {cluster_id}", log_file)
                log_message(f"  URL: {result['url']}", log_file)
                
                if result['success']:
                    saved, save_msg = save_result(result)
                    if saved:
                        log_message(f"  ✓ Success: {save_msg}", log_file)
                        success_count += 1
                    else:
                        log_message(f"  ⚠ Warning: {save_msg}", log_file)
                        error_count += 1
                else:
                    save_result(result)
                    log_message(f"  ✗ Error: {result['error']}", log_file)
                    error_count += 1
                
                log_message("  " + "-" * 40, log_file)
                
                # Add delay between processing (not between requests)
                if args.delay > 0:
                    time.sleep(args.delay)
    else:
        # Sequential processing
        for i, cluster_id in enumerate(CLUSTER_IDS, 1):
            log_message(f"[{i}/{len(CLUSTER_IDS)}] Processing cluster: {cluster_id}", log_file)
            
            result = call_cluster_api(cluster_id, args.timeout)
            log_message(f"  URL: {result['url']}", log_file)
            
            if result['success']:
                saved, save_msg = save_result(result)
                if saved:
                    log_message(f"  ✓ Success: {save_msg}", log_file)
                    success_count += 1
                else:
                    log_message(f"  ⚠ Warning: {save_msg}", log_file)
                    error_count += 1
            else:
                save_result(result)
                log_message(f"  ✗ Error: {result['error']}", log_file)
                error_count += 1
            
            log_message("  " + "-" * 40, log_file)
            
            # Add delay between requests
            if args.delay > 0 and i < len(CLUSTER_IDS):
                time.sleep(args.delay)
    
    # Summary
    end_time = datetime.now()
    duration = end_time - start_time
    
    log_message("", log_file)
    log_message(f"Batch processing completed at {end_time}", log_file)
    log_message(f"Total duration: {duration}", log_file)
    log_message(f"Results saved in: {OUTPUT_DIR}", log_file)
    log_message(f"Log file: {log_file}", log_file)
    log_message("", log_file)
    log_message("Summary:", log_file)
    log_message(f"  Total clusters processed: {len(CLUSTER_IDS)}", log_file)
    log_message(f"  Successful requests: {success_count}", log_file)
    log_message(f"  Failed requests: {error_count}", log_file)
    log_message(f"  Success rate: {success_count/len(CLUSTER_IDS)*100:.1f}%", log_file)

if __name__ == "__main__":
    main()
