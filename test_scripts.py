#!/usr/bin/env python3
"""
Test script for batch cluster inspection tools
Usage: python3 test_scripts.py
"""

import json
import os
import tempfile
import shutil
from unittest.mock import patch, MagicMock

def create_mock_api_response(cluster_id, has_risk_ingress=True, has_risk_service=True):
    """Create a mock API response for testing"""
    response = {
        "data": {
            "ClusterName": f"test-cluster-{cluster_id}",
            "ClusterID": cluster_id,
            "ClusterRegion": "ap-guangzhou",
            "TotalCLBCount": 5,
            "TotalServiceCount": 10,
            "TotalIngressCount": 3,
            "RiskIngress": [],
            "RiskService": []
        }
    }
    
    if has_risk_ingress:
        response["data"]["RiskIngress"] = [
            f"clusterID/{cluster_id}/namespace/default/name/test-ingress-1/lbid/lb-test123",
            f"clusterID/{cluster_id}/namespace/kube-system/name/test-ingress-2/lbid/lb-test456"
        ]
    
    if has_risk_service:
        response["data"]["RiskService"] = [
            f"clusterID/{cluster_id}/namespace/default/name/test-service-1/lbid/lb-test789",
            f"clusterID/{cluster_id}/namespace/production/name/test-service-2/lbid/lb-test101"
        ]
    
    return response

def test_extract_risk_data():
    """Test the extract_risk_data function"""
    print("Testing extract_risk_data function...")
    
    # Import the function from our script
    import sys
    sys.path.append('.')
    
    try:
        from batch_cluster_inspection import extract_risk_data
    except ImportError:
        print("  ❌ Could not import extract_risk_data function")
        return False
    
    # Test with mock data
    cluster_id = "cls-test123"
    mock_response = create_mock_api_response(cluster_id)
    
    result = extract_risk_data(mock_response, cluster_id)
    
    # Verify results
    if result['cluster_id'] != cluster_id:
        print(f"  ❌ Cluster ID mismatch: expected {cluster_id}, got {result['cluster_id']}")
        return False
    
    if len(result['risk_ingress']) != 2:
        print(f"  ❌ RiskIngress count mismatch: expected 2, got {len(result['risk_ingress'])}")
        return False
    
    if len(result['risk_service']) != 2:
        print(f"  ❌ RiskService count mismatch: expected 2, got {len(result['risk_service'])}")
        return False
    
    print("  ✅ extract_risk_data function works correctly")
    return True

def test_aggregate_risk_data():
    """Test the aggregate_risk_data function"""
    print("Testing aggregate_risk_data function...")
    
    try:
        from batch_cluster_inspection import extract_risk_data, aggregate_risk_data
    except ImportError:
        print("  ❌ Could not import required functions")
        return False
    
    # Create test data for multiple clusters
    test_clusters = ["cls-test1", "cls-test2", "cls-test3"]
    all_risk_data = []
    
    for cluster_id in test_clusters:
        mock_response = create_mock_api_response(cluster_id)
        risk_data = extract_risk_data(mock_response, cluster_id)
        all_risk_data.append(risk_data)
    
    # Test aggregation
    aggregated = aggregate_risk_data(all_risk_data)
    
    # Verify aggregation results
    if aggregated['summary']['total_clusters'] != 3:
        print(f"  ❌ Total clusters mismatch: expected 3, got {aggregated['summary']['total_clusters']}")
        return False
    
    if aggregated['summary']['clusters_with_risk_ingress'] != 3:
        print(f"  ❌ Clusters with risk ingress mismatch: expected 3, got {aggregated['summary']['clusters_with_risk_ingress']}")
        return False
    
    if aggregated['summary']['total_risk_ingress_count'] != 6:  # 2 per cluster * 3 clusters
        print(f"  ❌ Total risk ingress count mismatch: expected 6, got {aggregated['summary']['total_risk_ingress_count']}")
        return False
    
    print("  ✅ aggregate_risk_data function works correctly")
    return True

def test_analysis_script():
    """Test the analysis script with mock data"""
    print("Testing analysis script...")
    
    try:
        from analyze_risk_data import analyze_risk_patterns
    except ImportError:
        print("  ❌ Could not import analyze_risk_patterns function")
        return False
    
    # Create mock aggregated data
    mock_data = {
        'summary': {
            'total_clusters': 3,
            'clusters_with_risk_ingress': 2,
            'clusters_with_risk_service': 3,
            'total_risk_ingress_count': 4,
            'total_risk_service_count': 6
        },
        'risk_ingress': {
            'all_entries': [
                {'cluster_id': 'cls-test1', 'entry': 'clusterID/cls-test1/namespace/default/name/ing1/lbid/lb1'},
                {'cluster_id': 'cls-test2', 'entry': 'clusterID/cls-test2/namespace/default/name/ing1/lbid/lb2'}
            ],
            'by_cluster': {
                'cls-test1': ['clusterID/cls-test1/namespace/default/name/ing1/lbid/lb1'],
                'cls-test2': ['clusterID/cls-test2/namespace/default/name/ing1/lbid/lb2']
            },
            'grouped_by_pattern': {
                'default/ing1': [
                    {'cluster_id': 'cls-test1', 'full_entry': 'clusterID/cls-test1/namespace/default/name/ing1/lbid/lb1'},
                    {'cluster_id': 'cls-test2', 'full_entry': 'clusterID/cls-test2/namespace/default/name/ing1/lbid/lb2'}
                ]
            }
        },
        'risk_service': {
            'all_entries': [
                {'cluster_id': 'cls-test1', 'entry': 'clusterID/cls-test1/namespace/default/name/svc1/lbid/lb1'},
                {'cluster_id': 'cls-test2', 'entry': 'clusterID/cls-test2/namespace/default/name/svc1/lbid/lb2'},
                {'cluster_id': 'cls-test3', 'entry': 'clusterID/cls-test3/namespace/default/name/svc1/lbid/lb3'}
            ],
            'by_cluster': {
                'cls-test1': ['clusterID/cls-test1/namespace/default/name/svc1/lbid/lb1'],
                'cls-test2': ['clusterID/cls-test2/namespace/default/name/svc1/lbid/lb2'],
                'cls-test3': ['clusterID/cls-test3/namespace/default/name/svc1/lbid/lb3']
            },
            'grouped_by_pattern': {
                'default/svc1': [
                    {'cluster_id': 'cls-test1', 'full_entry': 'clusterID/cls-test1/namespace/default/name/svc1/lbid/lb1'},
                    {'cluster_id': 'cls-test2', 'full_entry': 'clusterID/cls-test2/namespace/default/name/svc1/lbid/lb2'},
                    {'cluster_id': 'cls-test3', 'full_entry': 'clusterID/cls-test3/namespace/default/name/svc1/lbid/lb3'}
                ]
            }
        }
    }
    
    # Run analysis
    analysis = analyze_risk_patterns(mock_data)
    
    # Verify analysis results
    if len(analysis['cluster_analysis']) != 3:
        print(f"  ❌ Cluster analysis count mismatch: expected 3, got {len(analysis['cluster_analysis'])}")
        return False
    
    if 'default' not in analysis['namespace_analysis']:
        print("  ❌ Namespace analysis missing 'default' namespace")
        return False
    
    if len(analysis['cross_cluster_patterns']) != 2:  # Should have both ingress and service patterns
        print(f"  ❌ Cross-cluster patterns count mismatch: expected 2, got {len(analysis['cross_cluster_patterns'])}")
        return False
    
    if len(analysis['recommendations']) == 0:
        print("  ❌ No recommendations generated")
        return False
    
    print("  ✅ Analysis script works correctly")
    return True

def test_file_operations():
    """Test file operations and output generation"""
    print("Testing file operations...")
    
    # Create a temporary directory for testing
    with tempfile.TemporaryDirectory() as temp_dir:
        # Test JSON file creation
        test_data = {"test": "data", "numbers": [1, 2, 3]}
        test_file = os.path.join(temp_dir, "test.json")
        
        try:
            with open(test_file, 'w', encoding='utf-8') as f:
                json.dump(test_data, f, indent=2, ensure_ascii=False)
            
            # Test reading back
            with open(test_file, 'r', encoding='utf-8') as f:
                loaded_data = json.load(f)
            
            if loaded_data != test_data:
                print("  ❌ JSON file read/write mismatch")
                return False
            
        except Exception as e:
            print(f"  ❌ File operations failed: {e}")
            return False
    
    print("  ✅ File operations work correctly")
    return True

def run_integration_test():
    """Run a simple integration test"""
    print("Running integration test...")
    
    # This would test the actual API call, but we'll skip it to avoid making real requests
    print("  ⚠️  Integration test skipped (would require real API access)")
    return True

def main():
    """Run all tests"""
    print("=" * 60)
    print("BATCH CLUSTER INSPECTION TOOLS - TEST SUITE")
    print("=" * 60)
    print()
    
    tests = [
        ("File Operations", test_file_operations),
        ("Extract Risk Data", test_extract_risk_data),
        ("Aggregate Risk Data", test_aggregate_risk_data),
        ("Analysis Script", test_analysis_script),
        ("Integration Test", run_integration_test),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"Running {test_name}...")
        try:
            if test_func():
                passed += 1
            else:
                print(f"  ❌ {test_name} FAILED")
        except Exception as e:
            print(f"  ❌ {test_name} FAILED with exception: {e}")
        print()
    
    print("=" * 60)
    print(f"TEST RESULTS: {passed}/{total} tests passed")
    print("=" * 60)
    
    if passed == total:
        print("🎉 All tests passed! The tools are ready to use.")
        return 0
    else:
        print("⚠️  Some tests failed. Please check the implementation.")
        return 1

if __name__ == "__main__":
    exit(main())
