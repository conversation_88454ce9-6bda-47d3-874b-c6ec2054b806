#!/bin/bash

# Quick start script for batch cluster inspection
# This script runs the complete inspection and analysis pipeline

set -e  # Exit on any error

# Configuration
PYTHON_SCRIPT="batch_cluster_inspection.py"
ANALYSIS_SCRIPT="analyze_risk_data.py"
OUTPUT_DIR="cluster_inspection_results"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check dependencies
check_dependencies() {
    print_status "Checking dependencies..."
    
    # Check Python
    if ! command -v python3 &> /dev/null; then
        print_error "Python 3 is required but not installed"
        exit 1
    fi
    
    # Check requests library
    if ! python3 -c "import requests" &> /dev/null; then
        print_warning "Python requests library not found. Installing..."
        pip3 install requests || {
            print_error "Failed to install requests library"
            exit 1
        }
    fi
    
    # Check if scripts exist
    if [ ! -f "$PYTHON_SCRIPT" ]; then
        print_error "Python script $PYTHON_SCRIPT not found"
        exit 1
    fi
    
    if [ ! -f "$ANALYSIS_SCRIPT" ]; then
        print_error "Analysis script $ANALYSIS_SCRIPT not found"
        exit 1
    fi
    
    print_success "All dependencies checked"
}

# Function to run batch inspection
run_inspection() {
    print_status "Starting batch cluster inspection..."
    
    # Parse command line arguments for Python script
    PYTHON_ARGS=""
    while [[ $# -gt 0 ]]; do
        case $1 in
            --parallel|-p)
                PYTHON_ARGS="$PYTHON_ARGS --parallel $2"
                shift 2
                ;;
            --timeout|-t)
                PYTHON_ARGS="$PYTHON_ARGS --timeout $2"
                shift 2
                ;;
            --delay|-d)
                PYTHON_ARGS="$PYTHON_ARGS --delay $2"
                shift 2
                ;;
            --help|-h)
                echo "Usage: $0 [OPTIONS]"
                echo ""
                echo "Options:"
                echo "  --parallel, -p NUM    Number of parallel requests (default: 5)"
                echo "  --timeout, -t SEC     Request timeout in seconds (default: 30)"
                echo "  --delay, -d SEC       Delay between requests in seconds (default: 0.1)"
                echo "  --skip-analysis       Skip the analysis step"
                echo "  --help, -h            Show this help message"
                echo ""
                echo "Examples:"
                echo "  $0                           # Run with default settings"
                echo "  $0 --parallel 10 --timeout 60   # Use 10 parallel requests with 60s timeout"
                echo "  $0 --skip-analysis           # Only run inspection, skip analysis"
                exit 0
                ;;
            --skip-analysis)
                SKIP_ANALYSIS=true
                shift
                ;;
            *)
                print_warning "Unknown option: $1"
                shift
                ;;
        esac
    done
    
    # Run the Python inspection script
    print_status "Running batch inspection with args: $PYTHON_ARGS"
    if python3 "$PYTHON_SCRIPT" $PYTHON_ARGS; then
        print_success "Batch inspection completed successfully"
    else
        print_error "Batch inspection failed"
        exit 1
    fi
}

# Function to run analysis
run_analysis() {
    if [ "$SKIP_ANALYSIS" = true ]; then
        print_status "Skipping analysis as requested"
        return 0
    fi
    
    print_status "Running risk data analysis..."
    
    AGGREGATED_FILE="$OUTPUT_DIR/aggregated_risk_analysis.json"
    if [ ! -f "$AGGREGATED_FILE" ]; then
        print_error "Aggregated data file not found: $AGGREGATED_FILE"
        print_warning "Analysis skipped"
        return 1
    fi
    
    # Run analysis script
    if python3 "$ANALYSIS_SCRIPT" "$AGGREGATED_FILE"; then
        print_success "Risk analysis completed successfully"
    else
        print_error "Risk analysis failed"
        return 1
    fi
}

# Function to display results summary
show_summary() {
    print_status "Inspection and Analysis Summary"
    echo "================================"
    
    if [ -d "$OUTPUT_DIR" ]; then
        echo "Output directory: $OUTPUT_DIR"
        echo ""
        
        # Count result files
        result_count=$(find "$OUTPUT_DIR" -name "*_result.json" -type f | wc -l)
        error_count=$(find "$OUTPUT_DIR" -name "*_error.txt" -type f | wc -l)
        
        echo "Results:"
        echo "  - Successful API calls: $result_count"
        echo "  - Failed API calls: $error_count"
        echo ""
        
        # Show key output files
        echo "Generated files:"
        for file in "batch_execution.log" "aggregated_risk_analysis.json" "risk_summary.txt" "risk_analysis_report.txt"; do
            if [ -f "$OUTPUT_DIR/$file" ]; then
                echo "  ✓ $file"
            else
                echo "  ✗ $file (not found)"
            fi
        done
        echo ""
        
        # Show risk summary if available
        if [ -f "$OUTPUT_DIR/risk_summary.txt" ]; then
            echo "Risk Summary:"
            cat "$OUTPUT_DIR/risk_summary.txt" | head -10
            echo ""
        fi
        
        # Show analysis report summary if available
        if [ -f "risk_analysis_report.txt" ]; then
            echo "Analysis Report Summary:"
            head -20 "risk_analysis_report.txt" | tail -10
            echo ""
            echo "Full analysis report: risk_analysis_report.txt"
        fi
        
    else
        print_warning "Output directory not found"
    fi
}

# Function to cleanup on exit
cleanup() {
    if [ $? -ne 0 ]; then
        print_error "Script execution failed"
    fi
}

# Main execution
main() {
    echo "=========================================="
    echo "Batch Cluster Inspection Pipeline"
    echo "=========================================="
    echo ""
    
    # Set trap for cleanup
    trap cleanup EXIT
    
    # Check dependencies
    check_dependencies
    echo ""
    
    # Run inspection
    run_inspection "$@"
    echo ""
    
    # Run analysis
    run_analysis
    echo ""
    
    # Show summary
    show_summary
    echo ""
    
    print_success "Pipeline completed successfully!"
    echo ""
    echo "Next steps:"
    echo "1. Review the risk analysis report: risk_analysis_report.txt"
    echo "2. Check detailed results in: $OUTPUT_DIR/"
    echo "3. Address high-priority recommendations from the analysis"
}

# Run main function with all arguments
main "$@"
