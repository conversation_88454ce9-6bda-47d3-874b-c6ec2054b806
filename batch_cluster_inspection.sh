#!/bin/bash

# Batch script to call cluster inspection API for multiple cluster IDs
# Usage: ./batch_cluster_inspection.sh

# Base URL for the API
BASE_URL="http://kateway.woa.com/inspection/cluster/info/get"

# Array of cluster IDs
CLUSTER_IDS=(
    "cls-pnocuy7o"
    "cls-8s9918je"
    "cls-mteeijug"
    "cls-2nyavit7"
    "cls-0sf7xq8h"
    "cls-e6antxq2"
    "cls-019ctobc"
    "cls-c3mtb8tw"
    "cls-okzcm7mk"
    "cls-0fvw4ql7"
    "cls-m29ot7ga"
    "cls-kbj8dces"
    "cls-97tqtjxu"
    "cls-ky7xdrzx"
    "cls-glmxm5i6"
    "cls-nsftw5og"
    "cls-irbazn32"
    "cls-242vz9pu"
    "cls-65s6l2y8"
    "cls-h05ox5x2"
    "cls-dazrr6e2"
    "cls-isccrwql"
    "cls-7gvwytdd"
    "cls-mi9lwsrb"
    "cls-371qkhf6"
    "cls-la3q70cx"
    "cls-mper5yyj"
    "cls-3c4eezcs"
    "cls-jcb5w9dm"
    "cls-rjzry0t9"
    "cls-13j49wqb"
    "cls-quti290e"
    "cls-5ycseuoq"
    "cls-bg6nw5ip"
    "cls-mqmg9agh"
    "cls-jubv4ejf"
    "cls-2bvoyng8"
    "cls-jrb1523h"
    "cls-gkohai36"
    "cls-qvk25nxu"
    "cls-43begdw1"
    "cls-qgrg0pcj"
    "cls-hs3tnhee"
    "cls-n4qoxw50"
    "cls-4aznd6j3"
    "cls-2300v0c4"
)

# Parameters
FILTER="clb"
REFRESH_CACHE="true"

# Output directory for results
OUTPUT_DIR="cluster_inspection_results"
mkdir -p "$OUTPUT_DIR"

# Log file
LOG_FILE="$OUTPUT_DIR/batch_execution.log"

echo "Starting batch cluster inspection at $(date)" | tee "$LOG_FILE"
echo "Total clusters to process: ${#CLUSTER_IDS[@]}" | tee -a "$LOG_FILE"
echo "----------------------------------------" | tee -a "$LOG_FILE"

# Counter for progress
counter=0
total=${#CLUSTER_IDS[@]}

# Process each cluster ID
for cluster_id in "${CLUSTER_IDS[@]}"; do
    counter=$((counter + 1))
    
    echo "[$counter/$total] Processing cluster: $cluster_id" | tee -a "$LOG_FILE"
    
    # Construct the full URL
    url="${BASE_URL}?clusterID=${cluster_id}&filter=${FILTER}&refreshCache=${REFRESH_CACHE}"
    
    # Output file for this cluster
    output_file="$OUTPUT_DIR/${cluster_id}_result.json"
    
    # Make the API call
    echo "  URL: $url" | tee -a "$LOG_FILE"
    
    if curl -s -o "$output_file" -w "HTTP Status: %{http_code}\n" "$url" | tee -a "$LOG_FILE"; then
        # Check if the response is valid JSON
        if jq empty "$output_file" 2>/dev/null; then
            echo "  ✓ Success: Response saved to $output_file" | tee -a "$LOG_FILE"
        else
            echo "  ⚠ Warning: Response may not be valid JSON" | tee -a "$LOG_FILE"
        fi
    else
        echo "  ✗ Error: Failed to call API for $cluster_id" | tee -a "$LOG_FILE"
    fi
    
    echo "  ----------------------------------------" | tee -a "$LOG_FILE"
    
    # Add a small delay to avoid overwhelming the server
    sleep 0.5
done

echo "Batch processing completed at $(date)" | tee -a "$LOG_FILE"
echo "Results saved in: $OUTPUT_DIR" | tee -a "$LOG_FILE"
echo "Log file: $LOG_FILE" | tee -a "$LOG_FILE"

# Generate summary
echo "" | tee -a "$LOG_FILE"
echo "Summary:" | tee -a "$LOG_FILE"
echo "  Total clusters processed: $total" | tee -a "$LOG_FILE"
success_count=$(find "$OUTPUT_DIR" -name "*_result.json" -type f | wc -l)
echo "  Success count: $success_count" | tee -a "$LOG_FILE"
echo "  Results directory: $OUTPUT_DIR" | tee -a "$LOG_FILE"

# Aggregate RiskIngress and RiskService data
if [ "$success_count" -gt 0 ]; then
    echo "" | tee -a "$LOG_FILE"
    echo "Aggregating RiskIngress and RiskService data..." | tee -a "$LOG_FILE"

    # Create aggregation files
    RISK_INGRESS_FILE="$OUTPUT_DIR/aggregated_risk_ingress.txt"
    RISK_SERVICE_FILE="$OUTPUT_DIR/aggregated_risk_service.txt"
    RISK_SUMMARY_FILE="$OUTPUT_DIR/risk_summary.txt"

    # Clear previous aggregation files
    > "$RISK_INGRESS_FILE"
    > "$RISK_SERVICE_FILE"
    > "$RISK_SUMMARY_FILE"

    echo "=== Aggregated RiskIngress Data ===" >> "$RISK_INGRESS_FILE"
    echo "=== Aggregated RiskService Data ===" >> "$RISK_SERVICE_FILE"

    total_risk_ingress=0
    total_risk_service=0
    clusters_with_risk_ingress=0
    clusters_with_risk_service=0

    # Process each successful result file
    for result_file in "$OUTPUT_DIR"/*_result.json; do
        if [ -f "$result_file" ]; then
            cluster_id=$(basename "$result_file" "_result.json")
            echo "" >> "$RISK_INGRESS_FILE"
            echo "Cluster: $cluster_id" >> "$RISK_INGRESS_FILE"
            echo "Cluster: $cluster_id" >> "$RISK_SERVICE_FILE"

            # Extract RiskIngress using jq if available, otherwise use grep
            if command -v jq >/dev/null 2>&1; then
                # Use jq for better JSON parsing
                risk_ingress=$(jq -r '.data.RiskIngress[]? // empty' "$result_file" 2>/dev/null)
                risk_service=$(jq -r '.data.RiskService[]? // empty' "$result_file" 2>/dev/null)

                # Also try alternative paths
                if [ -z "$risk_ingress" ]; then
                    risk_ingress=$(jq -r '.RiskIngress[]? // empty' "$result_file" 2>/dev/null)
                fi
                if [ -z "$risk_service" ]; then
                    risk_service=$(jq -r '.RiskService[]? // empty' "$result_file" 2>/dev/null)
                fi
            else
                # Fallback to grep-based extraction
                risk_ingress=$(grep -o '"RiskIngress":\s*\[[^]]*\]' "$result_file" 2>/dev/null | sed 's/"RiskIngress":\s*\[//g' | sed 's/\]$//g' | tr ',' '\n' | sed 's/^[[:space:]]*"//g' | sed 's/"[[:space:]]*$//g')
                risk_service=$(grep -o '"RiskService":\s*\[[^]]*\]' "$result_file" 2>/dev/null | sed 's/"RiskService":\s*\[//g' | sed 's/\]$//g' | tr ',' '\n' | sed 's/^[[:space:]]*"//g' | sed 's/"[[:space:]]*$//g')
            fi

            # Count and save RiskIngress
            if [ -n "$risk_ingress" ]; then
                echo "$risk_ingress" >> "$RISK_INGRESS_FILE"
                ingress_count=$(echo "$risk_ingress" | wc -l)
                total_risk_ingress=$((total_risk_ingress + ingress_count))
                clusters_with_risk_ingress=$((clusters_with_risk_ingress + 1))
            else
                echo "  No RiskIngress found" >> "$RISK_INGRESS_FILE"
            fi

            # Count and save RiskService
            if [ -n "$risk_service" ]; then
                echo "$risk_service" >> "$RISK_SERVICE_FILE"
                service_count=$(echo "$risk_service" | wc -l)
                total_risk_service=$((total_risk_service + service_count))
                clusters_with_risk_service=$((clusters_with_risk_service + 1))
            else
                echo "  No RiskService found" >> "$RISK_SERVICE_FILE"
            fi

            echo "" >> "$RISK_SERVICE_FILE"
        fi
    done

    # Generate summary
    {
        echo "=== Risk Analysis Summary ==="
        echo ""
        echo "Total clusters analyzed: $success_count"
        echo "Clusters with RiskIngress: $clusters_with_risk_ingress"
        echo "Clusters with RiskService: $clusters_with_risk_service"
        echo "Total RiskIngress entries: $total_risk_ingress"
        echo "Total RiskService entries: $total_risk_service"
        echo ""
        echo "Generated at: $(date)"
    } > "$RISK_SUMMARY_FILE"

    echo "  Risk aggregation completed:" | tee -a "$LOG_FILE"
    echo "    - RiskIngress data: $RISK_INGRESS_FILE" | tee -a "$LOG_FILE"
    echo "    - RiskService data: $RISK_SERVICE_FILE" | tee -a "$LOG_FILE"
    echo "    - Summary: $RISK_SUMMARY_FILE" | tee -a "$LOG_FILE"
    echo "    - Total RiskIngress entries: $total_risk_ingress" | tee -a "$LOG_FILE"
    echo "    - Total RiskService entries: $total_risk_service" | tee -a "$LOG_FILE"
fi
