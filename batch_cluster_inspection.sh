#!/bin/bash

# Batch script to call cluster inspection API for multiple cluster IDs
# Usage: ./batch_cluster_inspection.sh

# Base URL for the API
BASE_URL="http://kateway.woa.com/inspection/cluster/info/get"

# Array of cluster IDs
CLUSTER_IDS=(
    "cls-pnocuy7o"
    "cls-8s9918je"
    "cls-mteeijug"
    "cls-2nyavit7"
    "cls-0sf7xq8h"
    "cls-e6antxq2"
    "cls-019ctobc"
    "cls-c3mtb8tw"
    "cls-okzcm7mk"
    "cls-0fvw4ql7"
    "cls-m29ot7ga"
    "cls-kbj8dces"
    "cls-97tqtjxu"
    "cls-ky7xdrzx"
    "cls-glmxm5i6"
    "cls-nsftw5og"
    "cls-irbazn32"
    "cls-242vz9pu"
    "cls-65s6l2y8"
    "cls-h05ox5x2"
    "cls-dazrr6e2"
    "cls-isccrwql"
    "cls-7gvwytdd"
    "cls-mi9lwsrb"
    "cls-371qkhf6"
    "cls-la3q70cx"
    "cls-mper5yyj"
    "cls-3c4eezcs"
    "cls-jcb5w9dm"
    "cls-rjzry0t9"
    "cls-13j49wqb"
    "cls-quti290e"
    "cls-5ycseuoq"
    "cls-bg6nw5ip"
    "cls-mqmg9agh"
    "cls-jubv4ejf"
    "cls-2bvoyng8"
    "cls-jrb1523h"
    "cls-gkohai36"
    "cls-qvk25nxu"
    "cls-43begdw1"
    "cls-qgrg0pcj"
    "cls-hs3tnhee"
    "cls-n4qoxw50"
    "cls-4aznd6j3"
    "cls-2300v0c4"
)

# Parameters
FILTER="clb"
REFRESH_CACHE="true"

# Output directory for results
OUTPUT_DIR="cluster_inspection_results"
mkdir -p "$OUTPUT_DIR"

# Log file
LOG_FILE="$OUTPUT_DIR/batch_execution.log"

echo "Starting batch cluster inspection at $(date)" | tee "$LOG_FILE"
echo "Total clusters to process: ${#CLUSTER_IDS[@]}" | tee -a "$LOG_FILE"
echo "----------------------------------------" | tee -a "$LOG_FILE"

# Counter for progress
counter=0
total=${#CLUSTER_IDS[@]}

# Process each cluster ID
for cluster_id in "${CLUSTER_IDS[@]}"; do
    counter=$((counter + 1))
    
    echo "[$counter/$total] Processing cluster: $cluster_id" | tee -a "$LOG_FILE"
    
    # Construct the full URL
    url="${BASE_URL}?clusterID=${cluster_id}&filter=${FILTER}&refreshCache=${REFRESH_CACHE}"
    
    # Output file for this cluster
    output_file="$OUTPUT_DIR/${cluster_id}_result.json"
    
    # Make the API call
    echo "  URL: $url" | tee -a "$LOG_FILE"
    
    if curl -s -o "$output_file" -w "HTTP Status: %{http_code}\n" "$url" | tee -a "$LOG_FILE"; then
        # Check if the response is valid JSON
        if jq empty "$output_file" 2>/dev/null; then
            echo "  ✓ Success: Response saved to $output_file" | tee -a "$LOG_FILE"
        else
            echo "  ⚠ Warning: Response may not be valid JSON" | tee -a "$LOG_FILE"
        fi
    else
        echo "  ✗ Error: Failed to call API for $cluster_id" | tee -a "$LOG_FILE"
    fi
    
    echo "  ----------------------------------------" | tee -a "$LOG_FILE"
    
    # Add a small delay to avoid overwhelming the server
    sleep 0.5
done

echo "Batch processing completed at $(date)" | tee -a "$LOG_FILE"
echo "Results saved in: $OUTPUT_DIR" | tee -a "$LOG_FILE"
echo "Log file: $LOG_FILE" | tee -a "$LOG_FILE"

# Generate summary
echo "" | tee -a "$LOG_FILE"
echo "Summary:" | tee -a "$LOG_FILE"
echo "  Total clusters processed: $total" | tee -a "$LOG_FILE"
echo "  Success count: $(find "$OUTPUT_DIR" -name "*_result.json" -type f | wc -l)" | tee -a "$LOG_FILE"
echo "  Results directory: $OUTPUT_DIR" | tee -a "$LOG_FILE"
